import type { D1Database } from '@cloudflare/workers-types';
import { updateSinglePlatformAccount } from './database';
import {
  PlatformAccountData,
  PlatformAccountStats,
  PlatformAccountsCollection,
  BatchOperationResult,
  OperationResult,
  FetchResult,
  DataFetcher,
  FetcherConfig
} from './types';

/**
 * 创建标准的平台账号数据结构
 */
function createStandardAccountData(): Partial<PlatformAccountData> {
  return {
    stats: {
      followers: '0',
      total_reads: '0',
      total_income: '0',
      yesterday_reads: '0',
      yesterday_income: '0',
      credit_score: '0',
      can_withdraw_amount: '0'
    }
  };
}

// ==================== 外部API响应接口 ====================

interface ToutiaoIncomeResponse {
  code: number;
  message: string;
  data?: Array<{
    is_yesterday_income_ready?: boolean;
    lastday?: number;
    period?: number;
    title: string;
    type: string;
    total?: number;
    settle_info?: {
      settle_abstract?: {
        扣税额?: number;
        税前收益?: number;
        税后收益?: number;
      };
    };
  }>;
}

interface ToutiaoBenefitResponse {
  code: number;
  err_no: number;
  message: string;
  data?: {
    credit_info?: {
      score: number;
    };
    fans_num: number;
  };
}

interface ToutiaoAccountInfoResponse {
  code: number;
  message: string;
  data?: {
    user_info?: {
      id: string;
      screen_name: string;
      has_already_authentication: boolean;
    };
  };
}

interface ToutiaoPunishResponse {
  code: number;
  message: string;
  data?: {
    punish_list?: Array<{
      group_source: number;
      reason: string;
    }>;
  } | null;
}

// ==================== 核心批量处理引擎 ====================

/**
 * 通用批量处理引擎 - 消除所有重复代码
 */
export class BatchProcessor {
  constructor(private db: D1Database) {}

  /**
   * 执行批量操作
   */
  async execute(config: FetcherConfig): Promise<BatchOperationResult> {
    console.log(`=== 开始批量${config.description} ===`);
    
    let successCount = 0;
    let failureCount = 0;
    let skippedCount = 0;
    const results: FetchResult[] = [];

    try {
      // 获取所有平台账号
      const accounts = await this.getAllAccounts();
      
      if (accounts.length === 0) {
        return this.createResult(
          true,
          `没有找到需要${config.description}的平台账号`,
          0, 0, 0, 0,
          []
        );
      }

      console.log(`找到 ${accounts.length} 个平台账号需要${config.description}`);

      // 添加超时保护，避免Cloudflare 10秒限制
      const startTime = Date.now();
      const maxExecutionTime = 8000; // 8秒安全限制

      // 批量处理所有账号（串行处理，避免Cloudflare限制）
      for (let i = 0; i < accounts.length; i++) {
        // 检查执行时间
        const elapsedTime = Date.now() - startTime;
        if (elapsedTime > maxExecutionTime) {
          console.log(`⚠️ 接近Cloudflare时间限制 (${elapsedTime}ms)，停止处理。已处理 ${i} 个账号`);
          break;
        }

        const account = accounts[i];
        console.log(`[${i + 1}/${accounts.length}] ${config.description}账号 ${account.phone}... (已用时: ${elapsedTime}ms)`);

        try {
          // 执行数据获取
          const fetchResult = await config.fetcher(account.phone, account.sessionid);
          results.push(fetchResult);

          // 在处理下一个账号前添加短暂延迟，减少总执行时间
          if (i < accounts.length - 1) {
            console.log(`  等待200ms后处理下一个账号...`);
            await new Promise(resolve => setTimeout(resolve, 200));
          }

          if (fetchResult.success && fetchResult.data) {
            // 更新数据库
            const updateResult = await this.updateDatabase(account, fetchResult.data);
            
            if (updateResult.success) {
              successCount++;
              console.log(`  ✅ 账号 ${account.phone} ${config.description}成功`);
            } else {
              failureCount++;
              console.log(`  ❌ 账号 ${account.phone} 数据库更新失败: ${updateResult.message}`);
              
              // 更新结果状态
              results[results.length - 1] = {
                ...fetchResult,
                success: false,
                message: `数据库更新失败: ${updateResult.message}`
              };
            }
          } else {
            failureCount++;
            console.log(`  ❌ 账号 ${account.phone} ${config.description}失败: ${fetchResult.message}`);
          }
        } catch (error) {
          failureCount++;
          const errorMessage = `${config.description}异常: ${error}`;
          console.error(`  ❌ 账号 ${account.phone} ${errorMessage}`);
          
          results.push({
            success: false,
            phone: account.phone,
            message: errorMessage
          });
        }
      }

      const message = `${config.description}完成: 总计${accounts.length}个账号，成功${successCount}个，失败${failureCount}个，跳过${skippedCount}个`;
      console.log(`=== ${message} ===`);

      return this.createResult(
        true,
        message,
        accounts.length,
        successCount,
        failureCount,
        skippedCount,
        results
      );

    } catch (error) {
      const errorMessage = `批量${config.description}过程中发生错误: ${error}`;
      console.error(errorMessage);
      
      return this.createResult(
        false,
        errorMessage,
        0,
        successCount,
        failureCount,
        skippedCount,
        results
      );
    }
  }

  /**
   * 获取所有平台账号
   */
  private async getAllAccounts(): Promise<Array<{
    mainAccountId: number;
    phone: string;
    sessionid: string;
  }>> {
    const queryResult = await this.db.prepare(`
      SELECT main_account_id, platform_accounts_data FROM main_account_platform_data
    `).all<{ main_account_id: number; platform_accounts_data: string }>();

    const allMainAccountsData = queryResult.results || [];
    const allAccounts: Array<{
      mainAccountId: number;
      phone: string;
      sessionid: string;
    }> = [];

    for (const mainAccountData of allMainAccountsData) {
      try {
        const platformData: PlatformAccountsCollection = JSON.parse(mainAccountData.platform_accounts_data);
        
        for (const [phone, accountData] of Object.entries(platformData.accounts)) {
          if (accountData.sessionid) {
            allAccounts.push({
              mainAccountId: mainAccountData.main_account_id,
              phone,
              sessionid: accountData.sessionid
            });
          }
        }
      } catch (parseError) {
        console.error(`解析主账号 ${mainAccountData.main_account_id} 的平台数据失败:`, parseError);
      }
    }

    return allAccounts;
  }

  /**
   * 更新数据库
   */
  private async updateDatabase(
    account: { mainAccountId: number; phone: string; sessionid: string },
    data: Partial<PlatformAccountData>
  ): Promise<{ success: boolean; message: string }> {
    try {
      return await updateSinglePlatformAccount(
        this.db,
        account.mainAccountId,
        account.phone,
        data
      );
    } catch (error) {
      return {
        success: false,
        message: `数据库更新异常: ${error}`
      };
    }
  }

  /**
   * 创建统一的结果对象
   */
  private createResult(
    success: boolean,
    message: string,
    totalAccounts: number,
    successCount: number,
    failureCount: number,
    skippedCount: number,
    results: FetchResult[]
  ): BatchOperationResult {
    return {
      success,
      message,
      totalAccounts,
      successCount,
      failureCount,
      skippedCount,
      results
    };
  }
}

// ==================== HTTP请求工具函数 ====================

/**
 * 通用HTTP请求函数 - 带重试机制
 */
async function fetchWithRetry<T>(
  url: string,
  sessionid: string,
  maxRetries: number = 3
): Promise<T> {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
          'Accept': 'application/json, text/plain, */*',
          'Referer': 'https://mp.toutiao.com/profile_v4/analysis/income-overview',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
          'Cookie': `sessionid=${sessionid}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json() as T;

    } catch (error) {
      lastError = error as Error;
      
      if (attempt < maxRetries) {
        console.log(`  请求失败，${2 ** attempt}秒后重试 (${attempt}/${maxRetries}): ${lastError.message}`);
        await new Promise(resolve => setTimeout(resolve, 2 ** attempt * 1000));
      }
    }
  }

  throw lastError || new Error('请求失败');
}

// ==================== 具体的数据获取器实现 ====================

/**
 * 收益数据获取器 - 使用与Python文件一致的请求方式
 */
export const incomeDataFetcher: DataFetcher = async (phone: string, sessionid: string): Promise<OperationResult> => {
  console.log(`  开始获取账号 ${phone} 的收益数据...`);

  try {
    // 使用与Python文件完全一致的请求方式
    const response = await fetch('https://mp.toutiao.com/pgc/mp/income/income_statement_abstract?only_mid_income=false&days=30&app_id=1231', {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://mp.toutiao.com/profile_v4/analysis/income-overview',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Cookie': `sessionid=${sessionid}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json() as any;
    console.log(`  收到收益数据响应:`, data);

    if (!data || data.code !== 0 || !data.data) {
      return {
        success: false,
        phone,
        message: `收益数据获取失败: ${data?.message || '未知错误'}`
      };
    }

    // 解析收益数据 - 根据实际API响应结构
    let totalIncome = 0;
    let yesterdayIncome = 0;
    let canWithdrawAmount = 0;
    let isYesterdayIncomeReady = false;

    for (const item of (data.data as any[])) {
      if (item.type === 'total_income') {
        // 累计收益
        totalIncome = item.total || 0;
      } else if (item.type === 'period_income') {
        // 昨日收益
        yesterdayIncome = item.lastday || 0;
        isYesterdayIncomeReady = item.is_yesterday_income_ready || false;
      } else if (item.type === 'can_withdraw_amount') {
        // 可提现金额
        canWithdrawAmount = item.settle_info?.settle_abstract?.税后收益 || item.total || 0;
      }
    }

    console.log(`  ✅ 收益数据获取成功: 总收益=${totalIncome}, 昨日收益=${yesterdayIncome}, 可提现=${canWithdrawAmount}`);

    // 只返回收益相关字段，不影响其他统计数据
    return {
      success: true,
      phone,
      message: '收益数据获取成功',
      isYesterdayIncomeReady,
      data: {
        stats: {
          total_income: String(totalIncome),
          yesterday_income: String(yesterdayIncome),
          can_withdraw_amount: String(canWithdrawAmount)
        }
      } as Partial<PlatformAccountData>
    };

  } catch (error) {
    console.error(`  ❌ 收益数据获取失败:`, error);
    return {
      success: false,
      phone,
      message: `收益数据获取异常: ${error}`
    };
  }
};

/**
 * 信用分和粉丝数获取器 - 使用与Python文件一致的API端点
 */
export const creditAndFansFetcher: DataFetcher = async (phone: string, sessionid: string): Promise<OperationResult> => {
  console.log(`  开始获取账号 ${phone} 的信用分和粉丝数...`);

  try {
    // 使用与Python文件一致的URL和请求头
    const response = await fetch('https://mp.toutiao.com/mp/agw/creator_project/get_benefit_page_info?app_id=1231', {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://mp.toutiao.com/profile_v4/analysis/works-overall/all',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Cookie': `sessionid=${sessionid}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json() as any;
    console.log(`  收到信用分和粉丝数响应:`, data);

    // 根据实际响应结构解析数据
    let creditScore = 0;
    let fansNum = 0;

    if (data && data.data) {
      // 尝试从不同可能的字段中提取信用分
      if (data.data.credit_info && typeof data.data.credit_info.score === 'number') {
        creditScore = data.data.credit_info.score;
      } else if (data.data.credit_score) {
        creditScore = data.data.credit_score;
      } else if (data.data.score) {
        creditScore = data.data.score;
      }

      // 尝试从不同可能的字段中提取粉丝数
      if (typeof data.data.fans_num === 'number') {
        fansNum = data.data.fans_num;
      } else if (data.data.followers) {
        fansNum = data.data.followers;
      } else if (data.data.fan_count) {
        fansNum = data.data.fan_count;
      }

      console.log(`  ✅ 信用分和粉丝数获取成功: 信用分=${creditScore}, 粉丝数=${fansNum}`);
    } else {
      console.log(`  ⚠️ 响应数据结构异常，使用默认值`);
    }

    // 只返回信用分和粉丝数字段，不影响其他统计数据
    return {
      success: true,
      phone,
      message: '信用分和粉丝数获取成功',
      data: {
        stats: {
          credit_score: String(creditScore),
          followers: String(fansNum)
        }
      } as Partial<PlatformAccountData>
    };

  } catch (error) {
    console.error(`  ❌ 信用分和粉丝数获取失败:`, error);
    return {
      success: false,
      phone,
      message: `信用分和粉丝数获取异常: ${error}`
    };
  }
};

/**
 * 账号详细信息获取器 - 使用与Python文件一致的方式
 */
export const accountInfoFetcher: DataFetcher = async (phone: string, sessionid: string): Promise<OperationResult> => {
  console.log(`  开始获取账号 ${phone} 的详细信息...`);

  try {
    // 使用与Python文件一致的URL和请求方式
    const response = await fetch('https://mp.toutiao.com/profile_v4/index', {
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Priority': 'u=0, i',
        'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'cross-site',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': `sessionid=${sessionid}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const contentType = response.headers.get('content-type') || '';

    if (contentType.includes('application/json')) {
      // JSON响应处理
      const data = await response.json() as any;
      if (data.user) {
        const userInfo = data.user;
        const username = userInfo.screen_name || '';
        const homepageUrl = userInfo.id ? `https://www.toutiao.com/c/user/${userInfo.id}/` : '';
        const isVerified = userInfo.has_already_authentication ? '是' : '否';

        console.log(`  ✅ 账号详细信息获取成功 (JSON): 用户名=${username}, 实名=${isVerified}`);

        // 只返回账号信息相关的字段，不包含stats以避免覆盖统计数据
        return {
          success: true,
          phone,
          message: '账号详细信息获取成功',
          data: {
            username,
            homepage_url: homepageUrl,
            is_verified: isVerified
          }
        };
      }
    } else {
      // HTML响应处理 - 与Python文件一致的解析逻辑
      const htmlContent = await response.text();
      console.log(`  收到HTML响应，内容长度: ${htmlContent.length} 字符`);

      const userInfo = extractUserInfoFromHTML(htmlContent);

      if (userInfo.id || userInfo.screen_name) {
        const username = userInfo.screen_name || '';
        const homepageUrl = userInfo.id ? `https://www.toutiao.com/c/user/${userInfo.id}/` : '';
        const isVerified = userInfo.has_already_authentication ? '是' : '否';

        console.log(`  ✅ 账号详细信息获取成功 (HTML): 用户名=${username}, 实名=${isVerified}`);

        // 只返回账号信息相关的字段，不包含stats以避免覆盖统计数据
        return {
          success: true,
          phone,
          message: '账号详细信息获取成功',
          data: {
            username,
            homepage_url: homepageUrl,
            is_verified: isVerified
          }
        };
      } else {
        return {
          success: false,
          phone,
          message: '未能从HTML中提取到用户信息'
        };
      }
    }

    return {
      success: false,
      phone,
      message: '响应格式不正确'
    };

  } catch (error) {
    console.error(`  ❌ 账号详细信息获取失败:`, error);
    return {
      success: false,
      phone,
      message: `账号详细信息获取异常: ${error}`
    };
  }
};

/**
 * 从HTML内容中提取用户信息 - 与Python文件一致的解析逻辑
 */
function extractUserInfoFromHTML(htmlContent: string): any {
  const userInfo: any = {};

  try {
    // 找到"user"的位置
    const userPos = htmlContent.indexOf('"user"');
    if (userPos === -1) {
      console.log('  未找到 "user" 字段');
      return userInfo;
    }

    console.log(`  找到 "user" 字段，位置: ${userPos}`);

    // 从整个HTML开始搜索has_already_authentication（因为它在user之前）
    const authPattern = /"has_already_authentication":\s*(true|false)/;
    const authMatch = htmlContent.match(authPattern);
    if (authMatch) {
      const authValue = authMatch[1] === 'true';
      userInfo.has_already_authentication = authValue;
      const authStatus = authValue ? "已实名" : "未实名";
      console.log(`  实名状态: ${authStatus} (${authValue})`);
    }

    // 从"user"位置开始搜索，找到第一个"id"
    const searchStart = userPos;
    const idPattern = /"id":\s*(\d+)/;
    const idMatch = htmlContent.substring(searchStart).match(idPattern);
    if (idMatch) {
      userInfo.id = idMatch[1];
      console.log(`  找到ID: ${userInfo.id}`);
    }

    // 从"user"位置开始搜索，找到第一个"screen_name"
    const namePattern = /"screen_name":\s*"([^"]*)"/;
    const nameMatch = htmlContent.substring(searchStart).match(namePattern);
    if (nameMatch) {
      userInfo.screen_name = nameMatch[1];
      console.log(`  找到昵称: ${userInfo.screen_name}`);
    }

  } catch (error) {
    console.error('  解析用户信息时出错:', error);
  }

  return userInfo;
}

/**
 * 阅读量数据获取器 - 获取总阅读量和昨日阅读量
 */
export const readingDataFetcher: DataFetcher = async (phone: string, sessionid: string): Promise<OperationResult> => {
  console.log(`  开始获取账号 ${phone} 的阅读量数据...`);

  try {
    // 使用与Python文件完全一致的请求方式
    const response = await fetch('https://mp.toutiao.com/mp/fe_api/home/<USER>', {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://mp.toutiao.com/profile_v4/index',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Cookie': `sessionid=${sessionid}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    // 检查响应内容类型
    const contentType = response.headers.get('content-type') || '';
    console.log(`  阅读量数据响应类型: ${contentType}`);

    let totalReads = 0;
    let yesterdayReads = 0;

    if (contentType.includes('application/json')) {
      // JSON响应处理
      const data = await response.json() as any;
      console.log(`  收到阅读量JSON响应，数据结构:`, Object.keys(data));

      if (data && data.code === 0 && data.data) {
        // 查找阅读量字段，可能在多层嵌套中
        const searchInObject = (obj: any, path: string = ''): void => {
          if (obj && typeof obj === 'object') {
            for (const [key, value] of Object.entries(obj)) {
              const currentPath = path ? `${path}.${key}` : key;

              if (key === 'total_read_play_count' && typeof value === 'string') {
                totalReads = parseInt(value) || 0;
                console.log(`  找到总阅读量字段 ${currentPath}: ${totalReads}`);
              } else if (key === 'yesterday_read_count' && typeof value === 'string') {
                yesterdayReads = parseInt(value) || 0;
                console.log(`  找到昨日阅读量字段 ${currentPath}: ${yesterdayReads}`);
              } else if (key === 'read_count' && typeof value === 'string' && totalReads === 0) {
                totalReads = parseInt(value) || 0;
                console.log(`  找到备用总阅读量字段 ${currentPath}: ${totalReads}`);
              } else if (typeof value === 'object' && value !== null) {
                searchInObject(value, currentPath);
              }
            }
          }
        };

        searchInObject(data.data);
      }
    } else {
      // HTML响应处理
      const htmlContent = await response.text();
      console.log(`  收到阅读量HTML响应，内容长度: ${htmlContent.length} 字符`);

      try {
        // 查找 yesterday_read_count 字段
        const yesterdayMatch = htmlContent.match(/"yesterday_read_count":\s*"?(\d+)"?/);
        if (yesterdayMatch) {
          yesterdayReads = parseInt(yesterdayMatch[1]) || 0;
          console.log(`  找到昨日阅读量: ${yesterdayReads}`);
        }

        // 查找 total_read_play_count 字段
        const totalMatch = htmlContent.match(/"total_read_play_count":\s*"?(\d+)"?/);
        if (totalMatch) {
          totalReads = parseInt(totalMatch[1]) || 0;
          console.log(`  找到总阅读量: ${totalReads}`);
        }

        // 如果没找到 total_read_play_count，尝试查找其他可能的字段
        if (totalReads === 0) {
          const altTotalMatch = htmlContent.match(/"read_count":\s*"?(\d+)"?/);
          if (altTotalMatch) {
            totalReads = parseInt(altTotalMatch[1]) || 0;
            console.log(`  找到总阅读量(备用字段): ${totalReads}`);
          }
        }
      } catch (parseError) {
        console.log(`  HTML解析出错: ${parseError}`);
      }
    }

    console.log(`  ✅ 阅读量数据获取成功: 总阅读量=${totalReads}, 昨日阅读量=${yesterdayReads}`);

    // 只返回阅读量相关字段，不影响其他统计数据
    return {
      success: true,
      phone,
      message: '阅读量数据获取成功',
      data: {
        stats: {
          total_reads: String(totalReads),
          yesterday_reads: String(yesterdayReads)
        }
      } as Partial<PlatformAccountData>
    };

  } catch (error) {
    console.error(`  ❌ 阅读量数据获取失败:`, error);
    return {
      success: false,
      phone,
      message: `阅读量数据获取异常: ${error}`
    };
  }
};

/**
 * 草稿箱数量获取器 - 获取草稿箱文章数量
 */
export const draftsCountFetcher: DataFetcher = async (phone: string, sessionid: string): Promise<OperationResult> => {
  console.log(`  开始获取账号 ${phone} 的草稿箱数量...`);

  try {
    // 使用与Python文件完全一致的请求方式
    const response = await fetch('https://mp.toutiao.com/mp/agw/creator_center/draft_count?type=0&app_id=1231', {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://mp.toutiao.com/profile_v4/manage/draft',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Cookie': `sessionid=${sessionid}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json() as any;
    console.log(`  收到草稿箱数量响应:`, data);

    if (!data || data.code !== 0) {
      return {
        success: false,
        phone,
        message: `草稿箱数量获取失败: ${data?.message || '未知错误'}`
      };
    }

    // 解析草稿箱数量 - 根据实际响应结构 {"code": 0, "count": 1, "message": "success"}
    let draftsCount = 0;
    if (typeof data.count === 'number') {
      // 直接从响应根级别获取count字段
      draftsCount = data.count;
    } else if (data.data && typeof data.data.count === 'number') {
      // 如果count在data字段内
      draftsCount = data.data.count;
    } else if (typeof data.data === 'number') {
      // 如果data字段本身就是数字
      draftsCount = data.data;
    }

    console.log(`  ✅ 草稿箱数量获取成功: ${draftsCount}篇`);

    // 只返回草稿箱数量字段，不影响其他数据
    return {
      success: true,
      phone,
      message: '草稿箱数量获取成功',
      data: {
        drafts_count: String(draftsCount)
      } as Partial<PlatformAccountData>
    };

  } catch (error) {
    console.error(`  ❌ 草稿箱数量获取失败:`, error);
    return {
      success: false,
      phone,
      message: `草稿箱数量获取异常: ${error}`
    };
  }
};

/**
 * 账号状态获取器
 */
export const accountStatusFetcher: DataFetcher = async (phone: string, sessionid: string): Promise<OperationResult> => {
  console.log(`  开始获取账号 ${phone} 的状态信息...`);

  try {
    // 先通过accountInfoFetcher获取用户信息
    const accountInfoResult = await accountInfoFetcher(phone, sessionid);

    if (!accountInfoResult.success) {
      return {
        success: false,
        phone,
        message: '无法获取用户信息用于状态检查'
      };
    }

    // 从homepage_url中提取用户ID
    let userId = '';
    if (accountInfoResult.data?.homepage_url) {
      const urlMatch = accountInfoResult.data.homepage_url.match(/\/user\/(\d+)\//);
      if (urlMatch) {
        userId = urlMatch[1];
      }
    }

    if (!userId) {
      return {
        success: false,
        phone,
        message: '无法从用户信息中提取用户ID'
      };
    }

    console.log(`  获取到用户ID: ${userId}`);

    // 获取禁言状态 - 使用与Python文件一致的API端点（不需要Cookie）
    const punishUrl = `https://i.snssdk.com/author/appeal/punish/info/v1/?punish_type=2&app_id=0&crypto_uid=&validate_ticket=&uid=${userId}&punish_type_str=&aid=1231`;

    const punishResponse = await fetch(punishUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Priority': 'u=1, i',
        'Referer': `https://i.snssdk.com/feoffline/toutiao_appeal/v1/tpl/mute-detail.html?uid=${userId}&punish_type=2`,
        'Sec-Ch-Ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
        // 注意：不需要Cookie，按照Python文件的方式
      }
    });

    let accountStatus = '正常';
    if (punishResponse.ok) {
      try {
        const punishData = await punishResponse.json() as any;
        console.log(`  禁言状态API响应:`, punishData);

        // 根据实际测试结果确定的判断逻辑：
        // 正常状态: {"err_no": 1, "err_tips": "禁言已解除", "data": null}
        // 禁言状态: {"err_no": 0, "err_tips": "", "data": {...禁言详情}}
        if (punishData && punishData.err_no === 0) {
          accountStatus = '禁言';

          // 提取禁言详细信息
          if (punishData.data && punishData.data.punish_time) {
            console.log(`  🚨 检测到禁言状态！`);
            console.log(`  禁言时间: ${punishData.data.punish_time}`);
            console.log(`  禁言原因: ${punishData.data.reason || '未知'}`);
            console.log(`  审核规则: ${punishData.data.audit_rule || '未知'}`);
          } else {
            console.log(`  🚨 检测到禁言状态 (err_no=0)`);
          }
        } else if (punishData && punishData.err_no === 1) {
          accountStatus = '正常';
          console.log(`  ✅ 账号状态正常 (${punishData.err_tips || 'err_no=1'})`);
        } else {
          console.log(`  ⚠️ 未知的响应格式:`, punishData);
        }
      } catch (error) {
        console.log(`  解析禁言状态响应失败: ${error}`);
      }
    } else {
      console.log(`  获取禁言状态失败: HTTP ${punishResponse.status}`);
    }

    console.log(`  ✅ 账号状态获取成功: ${accountStatus}`);

    // 只返回账号状态字段，不包含stats以避免覆盖统计数据
    return {
      success: true,
      phone,
      message: '账号状态获取成功',
      data: {
        account_status: accountStatus
      }
    };

  } catch (error) {
    console.error(`  ❌ 账号状态获取失败:`, error);
    return {
      success: false,
      phone,
      message: `账号状态获取异常: ${error}`
    };
  }
};

/**
 * 全部信息获取器 - 组合所有数据获取
 */
export const allDataFetcher: DataFetcher = async (phone: string, sessionid: string): Promise<OperationResult> => {
  console.log(`  开始获取账号 ${phone} 的全部信息（串行模式）...`);

  try {
    // 串行获取所有数据，避免Cloudflare免费方案的并发限制
    console.log(`  [1/6] 获取收益数据...`);
    let incomeResult: OperationResult;
    try {
      incomeResult = await incomeDataFetcher(phone, sessionid);
    } catch (error) {
      incomeResult = {
        success: false,
        phone,
        message: `收益数据获取失败: ${error}`
      };
    }

    // 减少延迟时间以适应Cloudflare限制
    await new Promise(resolve => setTimeout(resolve, 100));

    console.log(`  [2/6] 获取信用分和粉丝数...`);
    let creditFansResult: OperationResult;
    try {
      creditFansResult = await creditAndFansFetcher(phone, sessionid);
    } catch (error) {
      creditFansResult = {
        success: false,
        phone,
        message: `信用分和粉丝数获取失败: ${error}`
      };
    }

    // 减少延迟时间以适应Cloudflare限制
    await new Promise(resolve => setTimeout(resolve, 100));

    console.log(`  [3/6] 获取账号详细信息...`);
    let accountInfoResult: OperationResult;
    try {
      accountInfoResult = await accountInfoFetcher(phone, sessionid);
    } catch (error) {
      accountInfoResult = {
        success: false,
        phone,
        message: `账号详细信息获取失败: ${error}`
      };
    }

    // 减少延迟时间以适应Cloudflare限制
    await new Promise(resolve => setTimeout(resolve, 100));

    console.log(`  [4/6] 获取账号状态...`);
    let statusResult: OperationResult;
    try {
      statusResult = await accountStatusFetcher(phone, sessionid);
    } catch (error) {
      statusResult = {
        success: false,
        phone,
        message: `账号状态获取失败: ${error}`
      };
    }

    // 减少延迟时间以适应Cloudflare限制
    await new Promise(resolve => setTimeout(resolve, 100));

    console.log(`  [5/6] 获取阅读量数据...`);
    let readingResult: OperationResult;
    try {
      readingResult = await readingDataFetcher(phone, sessionid);
    } catch (error) {
      readingResult = {
        success: false,
        phone,
        message: `阅读量数据获取失败: ${error}`
      };
    }

    // 减少延迟时间以适应Cloudflare限制
    await new Promise(resolve => setTimeout(resolve, 100));

    console.log(`  [6/6] 获取草稿箱数量...`);
    let draftsResult: OperationResult;
    try {
      draftsResult = await draftsCountFetcher(phone, sessionid);
    } catch (error) {
      draftsResult = {
        success: false,
        phone,
        message: `草稿箱数量获取失败: ${error}`
      };
    }

    // 创建标准数据结构
    const mergedData = createStandardAccountData();

    let hasAnySuccess = false;
    let isYesterdayIncomeReady = false;

    // 合并收益数据
    if (incomeResult.success && incomeResult.data?.stats) {
      Object.assign(mergedData.stats!, incomeResult.data.stats);
      isYesterdayIncomeReady = incomeResult.isYesterdayIncomeReady || false;
      hasAnySuccess = true;
      console.log(`  ✅ 收益数据合并成功`);
    } else {
      console.log(`  ❌ 收益数据获取失败: ${incomeResult.message}`);
    }

    // 合并信用分和粉丝数
    if (creditFansResult.success && creditFansResult.data?.stats) {
      mergedData.stats!.credit_score = creditFansResult.data.stats.credit_score || '0';
      mergedData.stats!.followers = creditFansResult.data.stats.followers || '0';
      hasAnySuccess = true;
      console.log(`  ✅ 信用分和粉丝数合并成功`);
    } else {
      console.log(`  ❌ 信用分和粉丝数获取失败: ${creditFansResult.message}`);
    }

    // 合并账号信息
    if (accountInfoResult.success && accountInfoResult.data) {
      Object.assign(mergedData, accountInfoResult.data);
      hasAnySuccess = true;
      console.log(`  ✅ 账号详细信息合并成功`);
    } else {
      console.log(`  ❌ 账号详细信息获取失败: ${accountInfoResult.message}`);
    }

    // 合并账号状态
    if (statusResult.success && statusResult.data) {
      Object.assign(mergedData, statusResult.data);
      hasAnySuccess = true;
      console.log(`  ✅ 账号状态合并成功`);
    } else {
      console.log(`  ❌ 账号状态获取失败: ${statusResult.message}`);
    }

    // 合并阅读量数据
    if (readingResult.success && readingResult.data?.stats) {
      mergedData.stats!.total_reads = readingResult.data.stats.total_reads || '0';
      mergedData.stats!.yesterday_reads = readingResult.data.stats.yesterday_reads || '0';
      hasAnySuccess = true;
      console.log(`  ✅ 阅读量数据合并成功`);
    } else {
      console.log(`  ❌ 阅读量数据获取失败: ${readingResult.message}`);
    }

    // 合并草稿箱数量
    if (draftsResult.success && draftsResult.data) {
      mergedData.drafts_count = draftsResult.data.drafts_count || '-';
      hasAnySuccess = true;
      console.log(`  ✅ 草稿箱数量合并成功`);
    } else {
      console.log(`  ❌ 草稿箱数量获取失败: ${draftsResult.message}`);
    }

    if (!hasAnySuccess) {
      return {
        success: false,
        phone,
        message: '所有数据获取都失败了'
      };
    }

    console.log(`  ✅ 全部信息获取完成`);

    return {
      success: true,
      phone,
      message: '全部信息获取成功',
      isYesterdayIncomeReady,
      data: mergedData
    };

  } catch (error) {
    console.error(`  ❌ 全部信息获取失败:`, error);
    return {
      success: false,
      phone,
      message: `全部信息获取异常: ${error}`
    };
  }
};

// ==================== 导出的批量处理函数 ====================

/**
 * 批量获取并更新收益数据
 */
export async function batchFetchAndUpdateIncomeOnly(db: D1Database): Promise<BatchOperationResult> {
  const processor = new BatchProcessor(db);
  return processor.execute({
    name: 'income',
    description: '收益数据更新',
    fetcher: incomeDataFetcher
  });
}

/**
 * 批量获取并更新信用分和粉丝数
 */
export async function batchFetchAndUpdateCreditAndFans(db: D1Database): Promise<BatchOperationResult> {
  const processor = new BatchProcessor(db);
  return processor.execute({
    name: 'creditFans',
    description: '信用分和粉丝数更新',
    fetcher: creditAndFansFetcher
  });
}

/**
 * 批量获取并更新账号详细信息
 */
export async function batchFetchAndUpdateAccountInfo(db: D1Database): Promise<BatchOperationResult> {
  const processor = new BatchProcessor(db);
  return processor.execute({
    name: 'accountInfo',
    description: '账号详细信息更新',
    fetcher: accountInfoFetcher
  });
}

/**
 * 批量获取并更新阅读量数据
 */
export async function batchFetchAndUpdateReadingData(db: D1Database): Promise<BatchOperationResult> {
  const processor = new BatchProcessor(db);
  return processor.execute({
    name: 'readingData',
    description: '阅读量数据更新',
    fetcher: readingDataFetcher
  });
}

/**
 * 批量获取并更新草稿箱数量
 */
export async function batchFetchAndUpdateDraftsCount(db: D1Database): Promise<BatchOperationResult> {
  const processor = new BatchProcessor(db);
  return processor.execute({
    name: 'draftsCount',
    description: '草稿箱数量更新',
    fetcher: draftsCountFetcher
  });
}

/**
 * 批量获取并更新账号状态
 */
export async function batchFetchAndUpdateAccountStatus(db: D1Database): Promise<BatchOperationResult> {
  const processor = new BatchProcessor(db);
  return processor.execute({
    name: 'accountStatus',
    description: '账号状态更新',
    fetcher: accountStatusFetcher
  });
}

/**
 * 批量获取并更新所有信息
 */
export async function batchFetchAndUpdateAllData(db: D1Database): Promise<BatchOperationResult> {
  const processor = new BatchProcessor(db);
  return processor.execute({
    name: 'allData',
    description: '全部信息更新',
    fetcher: allDataFetcher
  });
}

// ==================== 智能批量处理（保持兼容性）====================

/**
 * 智能批量获取数据 - 分阶段获取策略
 * 保持与旧版本完全兼容的接口
 */
export async function smartBatchFetchAndUpdateData(
  db: D1Database,
  ctx?: ExecutionContext
): Promise<BatchOperationResult> {
  console.log('=== 开始智能批量获取平台账号数据 ===');

  const processor = new BatchProcessor(db);
  const result = await processor.execute({
    name: 'smartBatch',
    description: '智能批量数据更新',
    fetcher: allDataFetcher
  });

  // 添加智能批量处理特有的字段
  const smartResult: BatchOperationResult = {
    ...result,
    phase: 'completed' as const
  };

  // 如果有执行上下文且数据获取完成，准备推送数据
  if (ctx && result.success && result.successCount > 0) {
    console.log('数据获取完成，准备按主账号分组推送数据...');

    try {
      // 按主账号分组整理数据 - 简化版本
      const mainAccountGroups = await groupResultsByMainAccount(db, result.results);

      smartResult.mainAccountGroups = mainAccountGroups;
      smartResult.needsPush = true;

      console.log(`=== 数据获取完成，共 ${Object.keys(mainAccountGroups).length} 个主账号需要推送数据 ===`);
    } catch (error) {
      console.error('分组数据失败:', error);
    }
  }

  return smartResult;
}

/**
 * 按主账号分组整理结果数据
 */
async function groupResultsByMainAccount(
  db: D1Database,
  results: FetchResult[]
): Promise<{ [mainAccountId: string]: any }> {
  const groups: { [mainAccountId: string]: any } = {};

  try {
    // 获取所有主账号的平台数据
    const queryResult = await db.prepare(`
      SELECT main_account_id, platform_accounts_data FROM main_account_platform_data
    `).all<{ main_account_id: number; platform_accounts_data: string }>();

    const allMainAccountsData = queryResult.results || [];

    for (const mainAccountData of allMainAccountsData) {
      try {
        const platformData: PlatformAccountsCollection = JSON.parse(mainAccountData.platform_accounts_data);
        const mainAccountId = String(mainAccountData.main_account_id);

        // 找到属于这个主账号的结果
        const accountPhones = Object.keys(platformData.accounts);
        const accountResults = results.filter(result =>
          accountPhones.indexOf(result.phone) !== -1
        );

        if (accountResults.length > 0) {
          groups[mainAccountId] = {
            mainAccountId: mainAccountData.main_account_id,
            platformAccounts: platformData.accounts,
            updateResults: accountResults
          };
        }
      } catch (parseError) {
        console.error(`解析主账号 ${mainAccountData.main_account_id} 数据失败:`, parseError);
      }
    }
  } catch (error) {
    console.error('查询主账号数据失败:', error);
  }

  return groups;
}

/**
 * 兼容性函数 - 保持与旧版本的兼容
 */
export async function batchFetchAndUpdateData(db: D1Database): Promise<BatchOperationResult> {
  return batchFetchAndUpdateAllData(db);
}

// ==================== 单个账号数据获取函数（兼容性） ====================

/**
 * 获取单个账号的数据（兼容旧版本）
 */
export async function fetchDataForAccount(
  phone: string,
  sessionid: string,
  currentUsername?: string,
  currentHomepageUrl?: string,
  currentIsVerified?: string
): Promise<FetchResult> {
  console.log(`开始获取账号 ${phone} 的数据...`);

  try {
    // 使用全部信息获取器
    return await allDataFetcher(phone, sessionid);

  } catch (error) {
    console.error(`获取账号 ${phone} 数据失败:`, error);
    return {
      success: false,
      phone,
      message: `数据获取异常: ${error}`
    };
  }
}

/**
 * 获取新账号的完整数据（兼容旧版本）
 */
export async function fetchDataForNewAccount(
  phone: string,
  sessionid: string,
  currentUsername?: string,
  currentHomepageUrl?: string,
  currentIsVerified?: string
): Promise<FetchResult> {
  console.log(`开始获取新账号 ${phone} 的完整数据...`);

  try {
    // 对于新账号，强制获取所有信息
    return await allDataFetcher(phone, sessionid);

  } catch (error) {
    console.error(`获取新账号 ${phone} 数据失败:`, error);
    return {
      success: false,
      phone,
      message: `新账号数据获取异常: ${error}`
    };
  }
}
