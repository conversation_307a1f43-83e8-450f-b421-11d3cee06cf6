import type { DurableObjectNamespace, D1Database, KVNamespace } from '@cloudflare/workers-types';

export interface Env {
  // 用户认证 Durable Object 绑定
  USER_AUTH_DO: DurableObjectNamespace;

  // D1 数据库绑定
  DB: D1Database;

  // 静态资源绑定
  ASSETS: { fetch: (request: Request) => Promise<Response> };

  // 管理 KV 绑定
  ADMIN_KV: KVNamespace;
}

export interface User {
  id: number;
  phone: string;
  password: string;
  created_at: string;
  last_login_at: string | null;
} 