/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "user-auth-system",
	"main": "src/index.ts",
	"compatibility_date": "2024-03-20",
	"routes": [
		{ "pattern": "manbu.03310729.xyz", "custom_domain": true }
	],
	"migrations": [
		{
			"new_sqlite_classes": [
				"MyDurableObject",
				"UserAuthDO"
			],
			"tag": "v1"
		},
		{
			"deleted_classes": [
				"MyDurableObject"
			],
			"tag": "v2"
		}
	],
	"durable_objects": {
		"bindings": [
			{
				"class_name": "UserAuthDO",
				"name": "USER_AUTH_DO"
			}
		]
	},
	"d1_databases": [
		{
			"binding": "DB",
			"database_name": "user_auth_db",
			"database_id": "37b98837-7b0b-425a-9d4c-862f2f4deba8"
		}
	],
	"kv_namespaces": [
		{
			"binding": "ADMIN_KV",
			"id": "40f4098c791448dca656b2f4d36ea06a",
			"preview_id": "40f4098c791448dca656b2f4d36ea06a"
		}
	],
	"assets": {
		"directory": "./public",
		"binding": "ASSETS"
	},
	"triggers": {
		"crons": ["0 2 * * *"]
	},
	"observability": {
		"enabled": true
	}
	/**
	 * Smart Placement
	 * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
	 */
	// "placement": { "mode": "smart" },

	/**
	 * Bindings
	 * Bindings allow your Worker to interact with resources on the Cloudflare Developer Platform, including
	 * databases, object storage, AI inference, real-time communication and more.
	 * https://developers.cloudflare.com/workers/runtime-apis/bindings/
	 */

	/**
	 * Environment Variables
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
	 */
	// "vars": { "MY_VARIABLE": "production_value" },
	/**
	 * Note: Use secrets to store sensitive data.
	 * https://developers.cloudflare.com/workers/configuration/secrets/
	 */

	/**
	 * Static Assets
	 * https://developers.cloudflare.com/workers/static-assets/binding/
	 */
	// "assets": { "directory": "./public/", "binding": "ASSETS" },

	/**
	 * Service Bindings (communicate between multiple Workers)
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings
	 */
	// "services": [{ "binding": "MY_SERVICE", "service": "my-service" }]
}
