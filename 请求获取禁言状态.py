import requests
import json

def get_punish_info(uid="1338527831037449", cookie_string=None):
    """
    获取用户禁言状态信息

    参数:
    uid: 用户ID，默认为示例ID
    cookie_string: 认证Cookie字符串，需要从浏览器开发者工具中获取

    如何获取Cookie:
    1. 打开浏览器开发者工具 (F12)
    2. 访问头条创作者平台并登录
    3. 在Network标签页中找到相关请求
    4. 复制请求头中的Cookie值
    """

    # 1. Define the URL from the GET request line
    # It's the host + the path/query parameters
    url = f"https://i.snssdk.com/author/appeal/punish/info/v1/?punish_type=2&app_id=0&crypto_uid=&validate_ticket=&uid={uid}&punish_type_str=&aid=1231"

    # 2. Define the headers as a dictionary
    # Copy all headers from your raw request, especially the 'Cookie' and 'User-Agent'
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Priority": "u=1, i",
        "Referer": f"https://i.snssdk.com/feoffline/toutiao_appeal/v1/tpl/mute-detail.html?uid={uid}&punish_type=2",
        "Sec-Ch-Ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"Windows"',
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
    }

    # 添加Cookie（如果提供）
    if cookie_string:
        headers["Cookie"] = cookie_string

    # 3. Send the GET request
    try:
        response = requests.get(url, headers=headers)

        # 4. Check the response and print the data
        # A status code of 200 means success!
        if response.status_code == 200:
            # Since the 'Accept' header asks for json, we can likely parse it as json
            data = response.json()
            print("✅ Request successful!")
            # Pretty-print the JSON data
            print(json.dumps(data, indent=2, ensure_ascii=False))
            return data
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            print("Response Text:", response.text)
            return None

    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
        return None


# 直接调用示例（需要提供有效的Cookie）
if __name__ == "__main__":
    # 示例调用 - 需要替换为实际的Cookie值
    # cookie = "your_actual_cookie_string_here"
    # result = get_punish_info(uid="4220339847964857", cookie_string=cookie)

    # 无Cookie的测试调用（可能会失败，但可以看到请求格式）
    print("测试获取禁言状态（无Cookie，可能失败）:")
    result = get_punish_info()
