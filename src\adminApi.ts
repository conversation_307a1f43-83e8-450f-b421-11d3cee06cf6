import type { D1Database, DurableObjectStub, Request as WorkerRequest, KVNamespace } from '@cloudflare/workers-types';
import { generateActivationCode, getActivationCodes, getActivationRecords, batchUpdatePlatformAccounts, getPlatformAccountsData, updateSinglePlatformAccount, deletePlatformAccount, transferPlatformAccount, batchTransferPlatformAccounts, validatePlatformAccountsCollection, getUserById, resetYesterdayData } from './database';
import { User, ActivationCodeType, PlatformAccountData } from './types';
import { jsonResponse, errorResponse } from './utils';
import { fetchDataForAccount, fetchDataForNewAccount, batchFetchAndUpdateData, batchFetchAndUpdateAllData, batchFetchAndUpdateIncomeOnly, batchFetchAndUpdateCreditAndFans, batchFetchAndUpdateAccountInfo, batchFetchAndUpdateAccountStatus } from './dataFetcher-new';


// 生成64位随机token
function generateAdminToken(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 64; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 管理员登录
export async function adminLogin(adminKV: KVNamespace, username: string, password: string): Promise<Response> {
  try {
    console.log('尝试管理员登录:', { username });
    
    // 检查adminKV是否为空
    if (!adminKV) {
      console.error('管理员KV命名空间未定义');
      return errorResponse('管理员KV命名空间未定义', 500);
    }
    
    const storedPassword = await adminKV.get('admin_password');
    const storedUsername = await adminKV.get('admin_username');
    
    console.log('KV存储状态:', { 
      hasStoredUsername: !!storedUsername, 
      hasStoredPassword: !!storedPassword 
    });
    
    if (!storedPassword || !storedUsername) {
      console.log('初始化管理员账号密码');
      // 如果KV中没有存储管理员信息，则初始化
      try {
        await adminKV.put('admin_username', 'manbuwangluo');
        await adminKV.put('admin_password', 'manbu2024');
        console.log('初始化完成');
      } catch (initError) {
        console.error('初始化管理员账号失败:', initError);
        return errorResponse(`初始化管理员账号失败: ${initError}`, 500);
      }
      
      // 重新获取存储的值
      if (username === 'manbuwangluo' && password === 'manbu2024') {
        // 生成新的管理员token
        const adminToken = generateAdminToken();

        // 保存token到KV，设置7天过期
        await adminKV.put('admin_token', adminToken, {
          expirationTtl: 7 * 24 * 60 * 60 // 7天
        });

        console.log('管理员登录成功，token已生成');

        return jsonResponse({
          success: true,
          message: '登录成功',
          token: adminToken
        });
      }
    } else {
      // 验证用户名和密码
      if (username === storedUsername && password === storedPassword) {
        // 生成新的管理员token
        const adminToken = generateAdminToken();

        // 保存token到KV，设置7天过期
        await adminKV.put('admin_token', adminToken, {
          expirationTtl: 7 * 24 * 60 * 60 // 7天
        });

        console.log('管理员登录成功，token已生成');

        return jsonResponse({
          success: true,
          message: '登录成功',
          token: adminToken
        });
      }
    }
    
    return errorResponse('用户名或密码错误', 401);
  } catch (error) {
    console.error('管理员登录失败:', error);
    return errorResponse(`管理员登录失败: ${error}`, 500);
  }
}

// 获取账号列表（包含在线状态）
export async function getAccounts(db: D1Database, userAuthDO: DurableObjectStub, phone?: string): Promise<Response> {
  try {
    let query = `
      SELECT
        users.id,
        users.phone,
        users.created_at,
        users.last_login_at,
        users.expiry_date,
        users.account_type,
        users.account_owner,
        owner.phone as owner_phone
      FROM users
      LEFT JOIN users as owner ON users.account_owner = owner.id
    `;
    const params: any[] = [];

    if (phone) {
      query += ` WHERE users.phone LIKE ?`;
      params.push(`%${phone}%`);
    }

    query += ` ORDER BY users.account_type ASC, users.created_at DESC`;

    const accounts = await db.prepare(query).bind(...params).all<User & { expiry_date: string | null }>();

    if (!accounts.results) {
      return jsonResponse({
        success: true,
        accounts: []
      });
    }

    // 批量获取在线状态
    const userIds = accounts.results.map(account => account.id);
    let onlineStatus: { [userId: number]: boolean } = {};

    if (userIds.length > 0) {
      try {
        const statusResponse = await userAuthDO.fetch(new Request('https://manbu.********.xyz/admin/get-all-online-status', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ userIds })
        }) as unknown as WorkerRequest);

        const statusData = await statusResponse.json<{ success: boolean; onlineStatus?: { [userId: number]: boolean } }>();
        if (statusData.success && statusData.onlineStatus) {
          onlineStatus = statusData.onlineStatus;
        }
      } catch (error) {
        console.error('批量获取在线状态失败:', error);
        // 如果获取在线状态失败，继续返回账号列表，但在线状态为false
      }
    }

    return jsonResponse({
      success: true,
      accounts: accounts.results.map((account: User & {
        expiry_date: string | null;
        owner_phone?: string;
      }) => ({
        id: account.id,
        phone: account.phone,
        registerTime: account.created_at,
        lastLoginTime: account.last_login_at,
        isOnline: onlineStatus[account.id] || false, // 从UserAuthDO获取的在线状态
        expiryDate: account.expiry_date,
        accountType: account.account_type,
        accountOwner: account.account_owner,
        ownerPhone: account.owner_phone
      }))
    });
  } catch (error) {
    console.error('获取账号列表失败:', error);
    return errorResponse('获取账号列表失败: ' + error, 500);
  }
}

// getLoginStatus函数已废弃，功能已集成到getAccounts中

// 删除账号
export async function deleteAccount(db: D1Database, phone: string): Promise<Response> {
  try {
    // 首先检查用户是否存在
    const user = await db.prepare('SELECT id FROM users WHERE phone = ?')
      .bind(phone)
      .first<{ id: number }>();
    
    if (!user) {
      return errorResponse('账号不存在', 404);
    }
    
    // 使用D1推荐的事务API
    const userId = user.id;
    
    // 执行删除操作 - 直接删除用户，不再需要删除sessions
    await db.prepare('DELETE FROM users WHERE id = ?').bind(userId).run();
    
    return jsonResponse({
      success: true,
      message: '账号删除成功'
    });
  } catch (error) {
    console.error('删除账号失败:', error);
    return errorResponse(`删除账号失败: ${error}`, 500);
  }
}

// forceLogout函数已废弃，强制下线功能已直接在index.ts中实现

// 生成激活码
export async function generateActivationCodes(db: D1Database, type: number, count: number): Promise<Response> {
  try {
    // 转换为枚举类型
    let codeType: ActivationCodeType;
    switch (type) {
      case 1:
        codeType = ActivationCodeType.DAY_1;
        break;
      case 7:
        codeType = ActivationCodeType.DAY_7;
        break;
      case 30:
        codeType = ActivationCodeType.DAY_30;
        break;
      case 90:
        codeType = ActivationCodeType.DAY_90;
        break;
      case 365:
        codeType = ActivationCodeType.DAY_365;
        break;
      default:
        return errorResponse('无效的激活码类型', 400);
    }
    
    const result = await generateActivationCode(db, codeType, count);
    
    if (!result.success) {
      return errorResponse(result.message, 500);
    }
    
    return jsonResponse({
      success: true,
      message: result.message,
      codes: result.codes
    });
  } catch (error) {
    console.error('生成激活码失败:', error);
    return errorResponse(`生成激活码失败: ${error}`, 500);
  }
}

// 获取激活码列表
export async function getActivationCodesList(db: D1Database, type?: number, used?: boolean): Promise<Response> {
  try {
    // 转换为枚举类型
    let codeType: ActivationCodeType | undefined = undefined;
    if (type !== undefined) {
      switch (type) {
        case 1:
          codeType = ActivationCodeType.DAY_1;
          break;
        case 7:
          codeType = ActivationCodeType.DAY_7;
          break;
        case 30:
          codeType = ActivationCodeType.DAY_30;
          break;
        case 90:
          codeType = ActivationCodeType.DAY_90;
          break;
        case 365:
          codeType = ActivationCodeType.DAY_365;
          break;
        default:
          return errorResponse('无效的激活码类型', 400);
      }
    }
    
    const result = await getActivationCodes(db, codeType, used);
    
    if (!result.success) {
      return errorResponse(result.message || '获取激活码列表失败', 500);
    }
    
    if (!result.codes) {
      return jsonResponse({
        success: true,
        codes: []
      });
    }
    
    return jsonResponse({
      success: true,
      codes: result.codes.map(code => ({
        id: code.id,
        code: code.code,
        type: code.type,
        createdAt: code.created_at,
        usedAt: code.used_at,
        usedBy: code.used_by
      }))
    });
  } catch (error) {
    console.error('获取激活码列表失败:', error);
    return errorResponse(`获取激活码列表失败: ${error}`, 500);
  }
}

// 获取激活记录
export async function getActivationRecordsList(db: D1Database): Promise<Response> {
  try {
    const result = await getActivationRecords(db);
    
    if (!result.success) {
      return errorResponse(result.message || '获取激活记录失败', 500);
    }
    
    if (!result.records) {
      return jsonResponse({
        success: true,
        records: []
      });
    }
    
    return jsonResponse({
      success: true,
      records: result.records.map(record => ({
        id: record.id,
        code: record.code,
        usedAt: record.used_at,
        phone: record.user_phone, // 使用用户表中的手机号
        days: record.days,
        userPhone: record.user_phone
      }))
    });
  } catch (error) {
    console.error('获取激活记录失败:', error);
    return errorResponse(`获取激活记录失败: ${error}`, 500);
  }
}

// 修改管理员密码
export async function changeAdminPassword(adminKV: KVNamespace, oldPassword: string, newPassword: string): Promise<Response> {
  try {
    console.log('尝试修改管理员密码');
    
    // 检查adminKV是否为空
    if (!adminKV) {
      console.error('管理员KV命名空间未定义');
      return errorResponse('管理员KV命名空间未定义', 500);
    }
    
    const storedPassword = await adminKV.get('admin_password');
    const storedUsername = await adminKV.get('admin_username');
    
    // 检查是否已初始化管理员账号
    if (!storedPassword || !storedUsername) {
      console.error('管理员账号未初始化');
      return errorResponse('管理员账号未初始化', 400);
    }
    
    // 验证旧密码是否正确
    if (oldPassword !== storedPassword) {
      return errorResponse('旧密码不正确', 401);
    }
    
    // 更新密码
    await adminKV.put('admin_password', newPassword);
    
    return jsonResponse({
      success: true,
      message: '密码修改成功'
    });
  } catch (error) {
    console.error('修改管理员密码失败:', error);
    return errorResponse(`修改管理员密码失败: ${error}`, 500);
  }
}

// 设置客户端最新版本
export async function setClientVersion(adminKV: KVNamespace, version: string): Promise<Response> {
  try {
    await adminKV.put('latest_client_version', version);
    return jsonResponse({
      success: true,
      message: `客户端最新版本已设置为 ${version}`
    });
  } catch (error) {
    console.error('设置客户端版本失败:', error);
    return errorResponse(`设置客户端版本失败: ${error}`, 500);
  }
}

// 获取客户端最新版本
export async function getClientVersion(adminKV: KVNamespace): Promise<Response> {
  try {
    const version = await adminKV.get('latest_client_version');
    return jsonResponse({
      success: true,
      version: version || '未设置'
    });
  } catch (error) {
    console.error('获取客户端版本失败:', error);
    return errorResponse(`获取客户端版本失败: ${error}`, 500);
  }
}

// 获取用户详细信息（包括账号类型和归属）
export async function getUserInfo(db: D1Database, phone: string): Promise<Response> {
  try {
    const user = await db.prepare(`
      SELECT
        users.id,
        users.phone,
        users.account_type,
        users.account_owner,
        users.created_at,
        users.last_login_at,
        users.expiry_date,
        owner.phone as owner_phone
      FROM users
      LEFT JOIN users as owner ON users.account_owner = owner.id
      WHERE users.phone = ?
    `).bind(phone).first<User & { owner_phone?: string }>();

    if (!user) {
      return errorResponse('用户不存在', 404);
    }

    return jsonResponse({
      success: true,
      user: {
        id: user.id,
        phone: user.phone,
        account_type: user.account_type,
        account_owner: user.account_owner,
        owner_phone: user.owner_phone,
        created_at: user.created_at,
        last_login_at: user.last_login_at,
        expiry_date: user.expiry_date
      }
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return errorResponse(`获取用户信息失败: ${error}`, 500);
  }
}

// 创建子账号
export async function createSubAccount(db: D1Database, ownerId: number, phone: string, password: string): Promise<Response> {
  try {
    // 验证主账号是否存在且为主账号类型
    const owner = await db.prepare(`SELECT id, account_type FROM users WHERE id = ?`).bind(ownerId).first();
    if (!owner) {
      return errorResponse('主账号不存在', 404);
    }

    if (owner.account_type !== '主账号') {
      return errorResponse('只有主账号才能创建子账号', 403);
    }

    // 导入registerUser函数
    const { registerUser } = await import('./database');

    // 创建子账号
    const result = await registerUser(db, phone, password, '子账号', ownerId);

    if (!result.success) {
      return errorResponse(result.message, 400);
    }

    return jsonResponse({
      success: true,
      message: result.message,
      subAccountId: result.userId
    });
  } catch (error) {
    console.error('创建子账号失败:', error);
    return errorResponse(`创建子账号失败: ${error}`, 500);
  }
}

// 获取子账号列表
export async function getSubAccountsList(db: D1Database, ownerId: number): Promise<Response> {
  try {
    // 验证主账号是否存在且为主账号类型
    const owner = await db.prepare(`SELECT id, account_type FROM users WHERE id = ?`).bind(ownerId).first();
    if (!owner) {
      return errorResponse('主账号不存在', 404);
    }

    if (owner.account_type !== '主账号') {
      return errorResponse('只有主账号才能查看子账号列表', 403);
    }

    // 导入getSubAccounts函数
    const { getSubAccounts } = await import('./database');

    const result = await getSubAccounts(db, ownerId);

    if (!result.success) {
      return errorResponse(result.message || '获取子账号列表失败', 500);
    }

    return jsonResponse({
      success: true,
      subAccounts: result.subAccounts?.map(account => ({
        id: account.id,
        phone: account.phone,
        created_at: account.created_at,
        last_login_at: account.last_login_at,
        expiry_date: account.expiry_date
      })) || []
    });
  } catch (error) {
    console.error('获取子账号列表失败:', error);
    return errorResponse(`获取子账号列表失败: ${error}`, 500);
  }
}

// 删除子账号
export async function deleteSubAccountAPI(db: D1Database, ownerId: number, subAccountId: number): Promise<Response> {
  try {
    // 导入deleteSubAccount函数
    const { deleteSubAccount } = await import('./database');

    const result = await deleteSubAccount(db, ownerId, subAccountId);

    if (!result.success) {
      return errorResponse(result.message, result.message.includes('权限') ? 403 : 400);
    }

    return jsonResponse({
      success: true,
      message: result.message
    });
  } catch (error) {
    console.error('删除子账号失败:', error);
    return errorResponse(`删除子账号失败: ${error}`, 500);
  }
}

// 获取平台账号信息列表
export async function getPlatformAccountsListAPI(db: D1Database, userId: number, isMainAccount: boolean, platformPhone?: string): Promise<Response> {
  try {
    const phones = platformPhone ? [platformPhone] : undefined;
    const result = await getPlatformAccountsData(db, userId, phones);

    if (!result.success) {
      return errorResponse(result.message || '获取平台账号信息列表失败', 500);
    }

    // 转换为数组格式，统一使用phone字段作为标识符
    const accountInfos = Object.entries(result.accounts || {}).map(([phone, data]) => ({
      ...data,
      // 确保phone字段正确设置，移除重复的platform_phone字段
      phone: phone
    }));

    return jsonResponse({
      success: true,
      accountInfos: accountInfos
    });
  } catch (error) {
    console.error('获取平台账号信息列表失败:', error);
    return errorResponse(`获取平台账号信息列表失败: ${error}`, 500);
  }
}

// 获取所有主账号的平台账号信息列表（管理员专用）
export async function getAllMainAccountsPlatformAccountsAPI(db: D1Database, platformPhone?: string): Promise<Response> {
  try {
    // 获取所有主账号
    const mainAccounts = await db.prepare(`
      SELECT id, phone FROM users WHERE account_type = '主账号'
    `).all<{ id: number; phone: string }>();

    if (!mainAccounts.results) {
      return jsonResponse({
        success: true,
        accountInfos: []
      });
    }

    const allAccountInfos: any[] = [];

    // 遍历每个主账号，获取其平台账号数据
    for (const mainAccount of mainAccounts.results) {
      const result = await db.prepare(`
        SELECT platform_accounts_data FROM main_account_platform_data WHERE main_account_id = ?
      `).bind(mainAccount.id).first<{ platform_accounts_data: string }>();

      if (result) {
        // 使用验证函数解析JSON数据
        const platformData = validatePlatformAccountsCollection(result.platform_accounts_data);
        if (platformData) {
          // 获取该主账号体系下的所有用户信息，用于显示holder_phone
          const usersInSystem = await db.prepare(`
            SELECT id, phone FROM users
            WHERE id = ? OR account_owner = ?
          `).bind(mainAccount.id, mainAccount.id).all<{ id: number; phone: string }>();

          const userPhoneMap = new Map(usersInSystem.results?.map(u => [u.id, u.phone]) || []);

          // 转换为数组格式并添加主账号信息
          const accountInfos = Object.entries(platformData.accounts).map(([phone, data]) => ({
            ...data,
            phone: phone,
            main_account_id: mainAccount.id,
            main_account_phone: mainAccount.phone,
            holder_phone: userPhoneMap.get(data.current_holder_id) || mainAccount.phone
          }));

          // 如果指定了平台手机号，只返回匹配的
          if (platformPhone) {
            const filteredAccounts = accountInfos.filter(account =>
              account.phone.includes(platformPhone)
            );
            allAccountInfos.push(...filteredAccounts);
          } else {
            allAccountInfos.push(...accountInfos);
          }
        }
      }
    }

    return jsonResponse({
      success: true,
      accountInfos: allAccountInfos
    });
  } catch (error) {
    console.error('获取所有主账号平台账号信息失败:', error);
    return errorResponse(`获取所有主账号平台账号信息失败: ${error}`, 500);
  }
}

// 批量更新平台账号信息
export async function batchUpdatePlatformAccountsAPI(db: D1Database, userId: number, accountsData: { [phone: string]: PlatformAccountData }, userAuthDO?: any): Promise<Response> {
  try {
    const result = await batchUpdatePlatformAccounts(db, userId, accountsData);

    if (!result.success) {
      return errorResponse(result.message, 400);
    }

    // 批量获取新添加账号的数据并推送
    await batchFetchAndPushNewAccounts(db, userId, accountsData, userAuthDO);

    return jsonResponse({
      success: true,
      message: result.message + "，收益数据已开始获取"
    });
  } catch (error) {
    console.error('批量更新平台账号信息失败:', error);
    return errorResponse(`批量更新平台账号信息失败: ${error}`, 500);
  }
}

// 添加平台账号信息（兼容性API）
export async function addPlatformAccountAPI(db: D1Database, accountData: PlatformAccountData, ownerId: number, currentHolderId?: number, userAuthDO?: any): Promise<Response> {
  try {
    console.log(`添加新平台账号 ${accountData.phone}，尝试获取真实数据...`);

    // 如果有sessionid，尝试获取真实数据
    let finalAccountData = { ...accountData };
    if (accountData.sessionid && accountData.sessionid.trim() !== '') {
      try {
        // 动态导入数据获取器
        const { allDataFetcher } = await import('./dataFetcher-new');
        const result = await allDataFetcher(accountData.phone, accountData.sessionid);

        if (result.success && result.data?.stats) {
          console.log(`成功获取账号 ${accountData.phone} 的真实数据`);
          // 合并真实数据
          finalAccountData.stats = { ...finalAccountData.stats, ...result.data.stats };
          if (result.data.username) finalAccountData.username = result.data.username;
          if (result.data.is_verified) finalAccountData.is_verified = result.data.is_verified;
          if (result.data.account_status) finalAccountData.account_status = result.data.account_status;
          finalAccountData.data_update_time = new Date().toISOString();
        } else {
          console.log(`获取账号 ${accountData.phone} 真实数据失败，使用默认值: ${result.message}`);
        }
      } catch (error) {
        console.error(`获取账号 ${accountData.phone} 真实数据异常:`, error);
      }
    }

    // 使用批量更新API添加单个账号
    const result = await batchUpdatePlatformAccounts(db, ownerId, {
      [finalAccountData.phone]: {
        ...finalAccountData,
        owner_id: ownerId,
        current_holder_id: currentHolderId || ownerId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    });

    if (!result.success) {
      return errorResponse(result.message, 400);
    }

    // 如果有UserAuthDO，推送通知给相关用户
    if (userAuthDO) {
      try {
        // 获取主账号体系内的所有用户
        const { getUserCompleteInfo } = await import('./database');
        const mainAccountInfo = await getUserCompleteInfo(db, ownerId);

        if (mainAccountInfo.success && mainAccountInfo.userInfo) {
          const allUsers = [mainAccountInfo.userInfo];
          if (mainAccountInfo.userInfo.sub_accounts) {
            allUsers.push(...mainAccountInfo.userInfo.sub_accounts);
          }

          console.log('管理员添加平台账号，准备推送给主账号体系内的用户，总数:', allUsers.length);

          // 推送给所有用户
          for (const user of allUsers) {
            const userNotificationData = {
              type: 'platform_account_received',
              message: `管理员为您添加了新的平台账号: ${accountData.phone} (${accountData.username || '未知用户名'})`,
              data: {
                addedByUserId: 0, // 管理员ID为0
                addedByUserPhone: '管理员',
                addedByUserType: '管理员',
                platformPhone: accountData.phone,
                platformUsername: accountData.username || '未知用户名',
                timestamp: new Date().toISOString(),
                isCurrentUser: false
              }
            };



            await userAuthDO.fetch(new Request('https://internal/notify-user', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                targetUserId: user.id,
                type: userNotificationData.type,
                message: userNotificationData.message,
                data: userNotificationData.data
              })
            }) as unknown as WorkerRequest);
          }
        }
      } catch (pushError) {
        console.error('推送管理员添加平台账号通知失败:', pushError);
        // 推送失败不影响主要功能，继续返回成功结果
      }
    }

    // 立即触发一次数据获取
    if (accountData.sessionid) {
      try {
        console.log(`新增平台账号 ${accountData.phone}，立即获取完整数据...`);
        const fetchResult = await fetchDataForNewAccount(
          accountData.phone,
          accountData.sessionid,
          accountData.username,
          accountData.homepage_url,
          accountData.is_verified
        );

        if (fetchResult.success && fetchResult.data) {
          // 更新刚添加的账号数据
          const updateData: Partial<PlatformAccountData> = {
            data_update_time: new Date().toISOString()
          };

          // 更新收益和统计数据
          if (fetchResult.data.stats) {
            updateData.stats = {
              ...accountData.stats,
              ...fetchResult.data.stats
            };
          }

          // 更新用户名（如果获取到了新的）
          if (fetchResult.data.username) {
            updateData.username = fetchResult.data.username;
            console.log(`更新用户名: ${accountData.username} -> ${fetchResult.data.username}`);
          }

          // 更新主页URL（如果获取到了新的）
          if (fetchResult.data.homepage_url) {
            updateData.homepage_url = fetchResult.data.homepage_url;
            console.log(`更新主页URL: ${accountData.homepage_url} -> ${fetchResult.data.homepage_url}`);
          }

          // 更新账号状态
          if (fetchResult.accountOffline) {
            updateData.account_status = '掉线';
          } else if (fetchResult.data.account_status) {
            // 如果获取到了新的账号状态（正常/禁言），使用新状态
            updateData.account_status = fetchResult.data.account_status;
            console.log(`更新账号状态: ${accountData.account_status} -> ${fetchResult.data.account_status}`);
          } else if (accountData.account_status === '掉线') {
            updateData.account_status = '正常';
          }

          // 更新实名认证状态（如果获取到了新的）
          if (fetchResult.data.is_verified) {
            updateData.is_verified = fetchResult.data.is_verified;
            console.log(`更新实名认证状态: ${accountData.is_verified || '未设置'} -> ${fetchResult.data.is_verified}`);
          }

          // 添加昨日收益准备状态
          if (fetchResult.isYesterdayIncomeReady !== undefined) {
            (updateData as any).is_yesterday_income_ready = fetchResult.isYesterdayIncomeReady;
          }

          // 更新数据库中的账号信息
          await updateSinglePlatformAccount(db, ownerId, accountData.phone, updateData);

          console.log(`新增平台账号 ${accountData.phone} 完整数据获取成功`);

          // 推送更新的平台账号数据给前端
          await pushNewPlatformAccountData(db, ownerId, accountData.phone, updateData, userAuthDO);
        } else {
          console.log(`新增平台账号 ${accountData.phone} 数据获取失败: ${fetchResult.message}`);
        }
      } catch (fetchError) {
        console.error(`新增平台账号 ${accountData.phone} 数据获取异常:`, fetchError);
        // 数据获取失败不影响账号添加的成功
      }
    }

    return jsonResponse({
      success: true,
      message: "平台账号添加成功，收益数据已开始获取"
    });
  } catch (error) {
    console.error('添加平台账号信息失败:', error);
    return errorResponse(`添加平台账号信息失败: ${error}`, 500);
  }
}

// 更新平台账号信息
export async function updatePlatformAccountAPI(db: D1Database, phone: string, userId: number, isMainAccount: boolean, accountData: Partial<PlatformAccountData>, currentHolderId?: number, userAuthDO?: any): Promise<Response> {
  try {
    const result = await updateSinglePlatformAccount(db, userId, phone, accountData);

    if (!result.success) {
      return errorResponse(result.message, 400);
    }

    // 如果更新成功且有UserAuthDO，推送更新的数据给前端
    if (userAuthDO) {
      try {
        await pushNewPlatformAccountData(db, userId, phone, accountData, userAuthDO);
        console.log(`平台账号 ${phone} 更新后数据推送成功`);
      } catch (pushError) {
        console.error('推送更新数据失败:', pushError);
        // 推送失败不影响更新操作的成功
      }
    }

    return jsonResponse({
      success: true,
      message: result.message
    });
  } catch (error) {
    console.error('更新平台账号信息失败:', error);
    return errorResponse(`更新平台账号信息失败: ${error}`, 500);
  }
}

// 删除平台账号信息
export async function deletePlatformAccountAPI(db: D1Database, phone: string, userId: number, userAuthDO?: any): Promise<Response> {
  try {
    // 先获取要删除的平台账号信息，用于推送通知
    const platformAccountsResult = await getPlatformAccountsData(db, userId, [phone]);
    let deletedAccountInfo = null;

    if (platformAccountsResult.success && platformAccountsResult.accounts && platformAccountsResult.accounts[phone]) {
      deletedAccountInfo = platformAccountsResult.accounts[phone];
    }

    const result = await deletePlatformAccount(db, userId, phone);

    if (!result.success) {
      return errorResponse(result.message, 400);
    }

    // 如果删除成功且有UserAuthDO，推送通知给相关用户
    if (userAuthDO && deletedAccountInfo) {
      try {
        // 获取用户信息用于推送
        const userInfo = await getUserById(db, userId);
        if (userInfo) {
          // 推送给管理员（如果在线）
          const adminNotificationData = {
            type: 'platform_account_deleted_notification',
            message: `用户 ${userInfo.phone} 删除了平台账号: ${phone} (${deletedAccountInfo.username || '未知用户名'})`,
            data: {
              userId: userId,
              userPhone: userInfo.phone,
              platformPhone: phone,
              platformUsername: deletedAccountInfo.username || '未知用户名',
              timestamp: new Date().toISOString()
            }
          };

          console.log('准备推送平台账号删除通知给管理员:', adminNotificationData);

          await userAuthDO.fetch(new Request('https://internal/notify-admin', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(adminNotificationData)
          }) as unknown as WorkerRequest);

          // 推送给主账号体系内的所有用户（包括主账号和子账号）
          // 获取主账号ID
          const mainAccountId = userInfo.account_owner || userId;

          // 获取主账号体系内的所有用户
          const { getUserCompleteInfo } = await import('./database');
          const mainAccountInfo = await getUserCompleteInfo(db, mainAccountId);

          if (mainAccountInfo.success && mainAccountInfo.userInfo) {
            const allUsers = [mainAccountInfo.userInfo];
            if (mainAccountInfo.userInfo.sub_accounts) {
              allUsers.push(...mainAccountInfo.userInfo.sub_accounts);
            }

            console.log('准备推送平台账号删除通知给主账号体系内的用户，总数:', allUsers.length);

            // 推送给所有用户（包括当前用户，用于多设备同步）
            for (const user of allUsers) {
              const userNotificationData = {
                type: 'platform_account_deleted_notification',
                message: user.id === userId
                  ? `您删除了平台账号: ${phone} (${deletedAccountInfo.username || '未知用户名'})`
                  : `${userInfo.account_type === '主账号' ? '主账号' : '子账号'} ${userInfo.phone} 删除了平台账号: ${phone} (${deletedAccountInfo.username || '未知用户名'})`,
                data: {
                  deletedByUserId: userId,
                  deletedByUserPhone: userInfo.phone,
                  deletedByUserType: userInfo.account_type,
                  platformPhone: phone,
                  platformUsername: deletedAccountInfo.username || '未知用户名',
                  timestamp: new Date().toISOString(),
                  isCurrentUser: user.id === userId
                }
              };

              console.log(`推送删除通知给用户 ${user.id} (${user.phone}):`, userNotificationData);

              await userAuthDO.fetch(new Request('https://internal/notify-user', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  targetUserId: user.id,
                  type: userNotificationData.type,
                  message: userNotificationData.message,
                  data: userNotificationData.data
                })
              }) as unknown as WorkerRequest);
            }
          }
        }
      } catch (pushError) {
        console.error('推送平台账号删除通知失败:', pushError);
        // 推送失败不影响主要功能，继续返回成功结果
      }
    }

    return jsonResponse({
      success: true,
      message: result.message
    });
  } catch (error) {
    console.error('删除平台账号信息失败:', error);
    return errorResponse(`删除平台账号信息失败: ${error}`, 500);
  }
}

// 转移平台账号
export async function transferPlatformAccountAPI(db: D1Database, phone: string, newHolderId: number, userId: number, userAuthDO?: any): Promise<Response> {
  try {
    // 先获取转移前的平台账号信息，用于推送通知
    const platformAccountsResult = await getPlatformAccountsData(db, userId, [phone]);
    let transferredAccountInfo = null;

    if (platformAccountsResult.success && platformAccountsResult.accounts && platformAccountsResult.accounts[phone]) {
      transferredAccountInfo = platformAccountsResult.accounts[phone];
    }

    const result = await transferPlatformAccount(db, userId, phone, newHolderId);

    if (!result.success) {
      return errorResponse(result.message, 400);
    }

    // 如果转移成功且有UserAuthDO，推送通知给相关用户
    if (userAuthDO && transferredAccountInfo) {
      try {
        // 获取操作者信息
        const operatorInfo = await getUserById(db, userId);
        // 获取新持有者信息
        const newHolderInfo = await getUserById(db, newHolderId);

        if (operatorInfo && newHolderInfo) {
          // 1. 推送给管理员（如果在线）
          const adminNotificationData = {
            type: 'platform_account_transferred_notification',
            message: `${operatorInfo.account_type} ${operatorInfo.phone} 将平台账号 ${phone} (${transferredAccountInfo.username || '未知用户名'}) 转移给了 ${newHolderInfo.account_type} ${newHolderInfo.phone}`,
            data: {
              operatorUserId: userId,
              operatorPhone: operatorInfo.phone,
              operatorType: operatorInfo.account_type,
              newHolderUserId: newHolderId,
              newHolderPhone: newHolderInfo.phone,
              newHolderType: newHolderInfo.account_type,
              platformPhone: phone,
              platformUsername: transferredAccountInfo.username || '未知用户名',
              timestamp: new Date().toISOString()
            }
          };

          console.log('准备推送平台账号转移通知给管理员:', adminNotificationData);

          await userAuthDO.fetch(new Request('https://internal/notify-admin', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(adminNotificationData)
          }) as unknown as WorkerRequest);

          // 2. 推送给相关主账号体系内的所有用户
          // 获取操作者的主账号体系
          const operatorMainAccountId = operatorInfo.account_owner || userId;
          const { getUserCompleteInfo } = await import('./database');
          const operatorMainAccountInfo = await getUserCompleteInfo(db, operatorMainAccountId);

          // 获取新持有者的主账号体系
          const newHolderMainAccountId = newHolderInfo.account_owner || newHolderId;
          const newHolderMainAccountInfo = await getUserCompleteInfo(db, newHolderMainAccountId);

          // 收集所有需要通知的用户
          const allUsersToNotify = new Set<number>();

          // 添加操作者主账号体系的用户
          if (operatorMainAccountInfo.success && operatorMainAccountInfo.userInfo) {
            allUsersToNotify.add(operatorMainAccountInfo.userInfo.id);
            if (operatorMainAccountInfo.userInfo.sub_accounts) {
              operatorMainAccountInfo.userInfo.sub_accounts.forEach(subAccount => {
                allUsersToNotify.add(subAccount.id);
              });
            }
          }

          // 添加新持有者主账号体系的用户
          if (newHolderMainAccountInfo.success && newHolderMainAccountInfo.userInfo) {
            allUsersToNotify.add(newHolderMainAccountInfo.userInfo.id);
            if (newHolderMainAccountInfo.userInfo.sub_accounts) {
              newHolderMainAccountInfo.userInfo.sub_accounts.forEach(subAccount => {
                allUsersToNotify.add(subAccount.id);
              });
            }
          }

          console.log('准备推送转移通知给用户，总数:', allUsersToNotify.size);

          // 推送给所有相关用户
          for (const targetUserId of allUsersToNotify) {
            const userNotificationData = {
              type: 'platform_account_received',
              message: targetUserId === newHolderId
                ? `您收到了来自 ${operatorInfo.account_type} ${operatorInfo.phone} 转移的平台账号: ${phone} (${transferredAccountInfo.username || '未知用户名'})`
                : targetUserId === userId
                ? `您将平台账号 ${phone} (${transferredAccountInfo.username || '未知用户名'}) 转移给了 ${newHolderInfo.account_type} ${newHolderInfo.phone}`
                : `${operatorInfo.account_type} ${operatorInfo.phone} 将平台账号 ${phone} (${transferredAccountInfo.username || '未知用户名'}) 转移给了 ${newHolderInfo.account_type} ${newHolderInfo.phone}`,
              data: {
                operatorUserId: userId,
                operatorPhone: operatorInfo.phone,
                operatorType: operatorInfo.account_type,
                newHolderUserId: newHolderId,
                newHolderPhone: newHolderInfo.phone,
                newHolderType: newHolderInfo.account_type,
                platformPhone: phone,
                platformUsername: transferredAccountInfo.username || '未知用户名',
                timestamp: new Date().toISOString(),
                isReceiver: targetUserId === newHolderId,
                isOperator: targetUserId === userId
              }
            };

            console.log(`推送转移通知给用户 ${targetUserId}:`, userNotificationData);

            await userAuthDO.fetch(new Request('https://internal/notify-user', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                targetUserId: targetUserId,
                type: userNotificationData.type,
                message: userNotificationData.message,
                data: userNotificationData.data
              })
            }) as unknown as WorkerRequest);
          }
        }
      } catch (pushError) {
        console.error('推送平台账号转移通知失败:', pushError);
        // 推送失败不影响主要功能，继续返回成功结果
      }
    }

    return jsonResponse({
      success: true,
      message: result.message
    });
  } catch (error) {
    console.error('转移平台账号失败:', error);
    return errorResponse(`转移平台账号失败: ${error}`, 500);
  }
}

// 批量转移平台账号（优化版，解决并发问题）
export async function batchTransferPlatformAccountsAPI(
  db: D1Database,
  userId: number,
  transfers: Array<{ phone: string; newHolderId: number }>,
  userAuthDO?: any
): Promise<Response> {
  try {
    const result = await batchTransferPlatformAccounts(db, userId, transfers);

    if (!result.success) {
      return errorResponse(result.message, 400);
    }

    const successCount = result.results.filter(r => r.success).length;
    const failCount = result.results.length - successCount;

    // 如果有成功的转移且有UserAuthDO，推送通知给相关用户
    if (successCount > 0 && userAuthDO) {
      try {
        // 获取操作者信息
        const operatorInfo = await getUserById(db, userId);

        if (operatorInfo) {
          // 获取成功转移的账号信息
          const successfulTransfers = result.results.filter(r => r.success);
          const platformAccountsResult = await getPlatformAccountsData(db, userId, successfulTransfers.map(t => t.phone));

          if (platformAccountsResult.success && platformAccountsResult.accounts) {
            // 1. 推送给管理员（如果在线）
            const transferList = successfulTransfers.map(t => {
              const accountInfo = platformAccountsResult.accounts![t.phone];
              return `${t.phone} (${accountInfo?.username || '未知用户名'})`;
            }).join(', ');

            const adminNotificationData = {
              type: 'platform_account_transferred_notification',
              message: `${operatorInfo.account_type} ${operatorInfo.phone} 批量转移了 ${successCount} 个平台账号: ${transferList}`,
              data: {
                operatorUserId: userId,
                operatorPhone: operatorInfo.phone,
                operatorType: operatorInfo.account_type,
                transferCount: successCount,
                transfers: successfulTransfers,
                timestamp: new Date().toISOString()
              }
            };

            console.log('准备推送批量平台账号转移通知给管理员:', adminNotificationData);

            await userAuthDO.fetch(new Request('https://internal/notify-admin', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(adminNotificationData)
            }) as unknown as WorkerRequest);

            // 2. 按新持有者分组推送通知
            const transfersByHolder = new Map<number, Array<{ phone: string; username: string }>>();

            for (const transfer of successfulTransfers) {
              const targetTransfer = transfers.find(t => t.phone === transfer.phone);
              if (targetTransfer) {
                const accountInfo = platformAccountsResult.accounts![transfer.phone];
                if (!transfersByHolder.has(targetTransfer.newHolderId)) {
                  transfersByHolder.set(targetTransfer.newHolderId, []);
                }
                transfersByHolder.get(targetTransfer.newHolderId)!.push({
                  phone: transfer.phone,
                  username: accountInfo?.username || '未知用户名'
                });
              }
            }

            // 收集所有需要通知的用户
            const allUsersToNotify = new Set<number>();

            // 添加操作者主账号体系的用户
            const operatorMainAccountId = operatorInfo.account_owner || userId;
            const { getUserCompleteInfo } = await import('./database');
            const operatorMainAccountInfo = await getUserCompleteInfo(db, operatorMainAccountId);

            if (operatorMainAccountInfo.success && operatorMainAccountInfo.userInfo) {
              allUsersToNotify.add(operatorMainAccountInfo.userInfo.id);
              if (operatorMainAccountInfo.userInfo.sub_accounts) {
                operatorMainAccountInfo.userInfo.sub_accounts.forEach(subAccount => {
                  allUsersToNotify.add(subAccount.id);
                });
              }
            }

            // 添加所有接收者主账号体系的用户
            for (const [newHolderId] of transfersByHolder) {
              const newHolderInfo = await getUserById(db, newHolderId);
              if (newHolderInfo) {
                const newHolderMainAccountId = newHolderInfo.account_owner || newHolderId;
                const newHolderMainAccountInfo = await getUserCompleteInfo(db, newHolderMainAccountId);

                if (newHolderMainAccountInfo.success && newHolderMainAccountInfo.userInfo) {
                  allUsersToNotify.add(newHolderMainAccountInfo.userInfo.id);
                  if (newHolderMainAccountInfo.userInfo.sub_accounts) {
                    newHolderMainAccountInfo.userInfo.sub_accounts.forEach(subAccount => {
                      allUsersToNotify.add(subAccount.id);
                    });
                  }
                }
              }
            }

            console.log('准备推送批量转移通知给用户，总数:', allUsersToNotify.size);

            // 推送给所有相关用户
            for (const targetUserId of allUsersToNotify) {
              // 检查用户是否是接收者
              let receivedAccounts: Array<{ phone: string; username: string }> = [];
              for (const [newHolderId, accounts] of transfersByHolder) {
                if (newHolderId === targetUserId) {
                  receivedAccounts = accounts;
                  break;
                }
              }

              let message: string;
              if (receivedAccounts.length > 0) {
                // 接收者
                const accountList = receivedAccounts.map(acc => `${acc.phone} (${acc.username})`).join(', ');
                message = `${operatorInfo.account_type} ${operatorInfo.phone} 批量转移了 ${receivedAccounts.length} 个平台账号给您: ${accountList}`;
              } else if (targetUserId === userId) {
                // 操作者
                message = `您批量转移了 ${successCount} 个平台账号`;
              } else {
                // 其他用户
                message = `${operatorInfo.account_type} ${operatorInfo.phone} 批量转移了 ${successCount} 个平台账号`;
              }

              const userNotificationData = {
                type: receivedAccounts.length > 0 ? 'platform_accounts_batch_received' : 'platform_account_received',
                message: message,
                data: {
                  operatorUserId: userId,
                  operatorPhone: operatorInfo.phone,
                  operatorType: operatorInfo.account_type,
                  transferCount: successCount,
                  receivedAccounts: receivedAccounts,
                  timestamp: new Date().toISOString(),
                  isReceiver: receivedAccounts.length > 0,
                  isOperator: targetUserId === userId
                }
              };



              await userAuthDO.fetch(new Request('https://internal/notify-user', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  targetUserId: targetUserId,
                  type: userNotificationData.type,
                  message: userNotificationData.message,
                  data: userNotificationData.data
                })
              }) as unknown as WorkerRequest);
            }
          }
        }
      } catch (pushError) {
        console.error('推送批量平台账号转移通知失败:', pushError);
        // 推送失败不影响主要功能，继续返回成功结果
      }
    }

    return jsonResponse({
      success: true,
      message: result.message,
      results: result.results,
      successCount: successCount,
      failCount: failCount
    });
  } catch (error) {
    console.error('批量转移平台账号失败:', error);
    return errorResponse(`批量转移平台账号失败: ${error}`, 500);
  }
}

// 根据平台手机号获取平台账号信息
export async function getPlatformAccountByPhoneAPI(db: D1Database, platformPhone: string, userId: number, isMainAccount: boolean): Promise<Response> {
  try {
    const result = await getPlatformAccountsData(db, userId, [platformPhone]);

    if (!result.success) {
      return errorResponse(result.message || '获取平台账号信息失败', 500);
    }

    const accountInfo = result.accounts?.[platformPhone] || null;

    return jsonResponse({
      success: true,
      accountInfo: accountInfo ? {
        ...accountInfo,
        platform_phone: platformPhone
      } : null
    });
  } catch (error) {
    console.error('获取平台账号信息失败:', error);
    return errorResponse(`获取平台账号信息失败: ${error}`, 500);
  }
}

// 兼容性API - 保持向后兼容
export async function getAccountInfoListAPI(db: D1Database, phone?: string): Promise<Response> {
  return jsonResponse({
    success: true,
    accountInfos: []
  });
}

export async function addAccountInfoAPI(db: D1Database, accountInfo: any): Promise<Response> {
  return errorResponse('请使用新的addPlatformAccountAPI', 400);
}

export async function updateAccountInfoAPI(db: D1Database, id: number, accountInfo: any): Promise<Response> {
  return errorResponse('请使用新的updatePlatformAccountAPI', 400);
}

/**
 * 推送新添加的平台账号数据给前端
 */
async function pushNewPlatformAccountData(
  db: D1Database,
  mainAccountId: number,
  phone: string,
  updateData: Partial<PlatformAccountData>,
  userAuthDO?: any
): Promise<void> {
  try {
    console.log(`开始推送新平台账号 ${phone} 的数据给前端...`);

    if (!userAuthDO) {
      console.log('UserAuthDO未提供，跳过推送');
      return;
    }

    // 获取完整的主账号平台数据
    const platformData = await getPlatformAccountsData(db, mainAccountId);
    if (!platformData.success || !platformData.accounts) {
      console.error('获取主账号平台数据失败，无法推送');
      return;
    }

    const mainAccountData = platformData.accounts;
    const platformAccounts: any[] = [];

    // 收集该主账号下的所有平台账号数据
    for (const [accountPhone, accountData] of Object.entries(mainAccountData)) {
      const typedAccountData = accountData as PlatformAccountData;
      platformAccounts.push({
        phone: accountPhone,
        platform: typedAccountData.platform || '头条号',
        username: typedAccountData.username || '',
        login_type: typedAccountData.login_type || '',
        team_tag: typedAccountData.team_tag || '',
        account_status: typedAccountData.account_status || '',
        is_verified: typedAccountData.is_verified || '否', // 实名认证状态
        stats: {
          total_income: typedAccountData.stats?.total_income || '0',
          yesterday_income: typedAccountData.stats?.yesterday_income || '0',
          can_withdraw_amount: typedAccountData.stats?.can_withdraw_amount || '0',
          yesterday_reads: typedAccountData.stats?.yesterday_reads || '0',
          total_reads: typedAccountData.stats?.total_reads || '0',
          credit_score: typedAccountData.stats?.credit_score || '0',
          followers: typedAccountData.stats?.followers || '0'
        },
        data_update_time: typedAccountData.data_update_time || '',
        // 标记新添加的账号
        isNewlyAdded: accountPhone === phone
      });
    }

    const groupData = {
      mainAccountId,
      platformAccounts,
      metadata: {
        last_batch_update: new Date().toISOString(),
        total_accounts: platformAccounts.length,
        updated_accounts: platformAccounts.filter(acc => acc.data_update_time).length
      }
    };

    // 1. 推送给管理员
    console.log('推送新平台账号数据给管理员...');
    const adminNotifyRequest = new Request('https://dummy.com/push-platform-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: 'new_platform_account_added',
        target: 'admin',
        data: {
          mainAccountGroups: {
            [mainAccountId]: groupData
          },
          newAccountPhone: phone,
          timestamp: new Date().toISOString()
        }
      })
    });

    const adminResponse = await userAuthDO.fetch(adminNotifyRequest as any);
    if (adminResponse.ok) {
      console.log('成功推送新平台账号数据给管理员');
    } else {
      console.error('推送管理员新平台账号数据失败:', await adminResponse.text());
    }

    // 2. 推送给该主账号的用户
    console.log(`推送新平台账号数据给主账号 ${mainAccountId} 的用户...`);
    const userNotifyRequest = new Request('https://dummy.com/push-platform-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: 'new_platform_account_added',
        target: 'user',
        mainAccountId: mainAccountId,
        data: {
          mainAccountData: groupData,
          newAccountPhone: phone,
          timestamp: new Date().toISOString()
        }
      })
    });

    const userResponse = await userAuthDO.fetch(userNotifyRequest as any);
    if (userResponse.ok) {
      console.log(`成功推送新平台账号数据给主账号 ${mainAccountId} 的用户`);
    } else {
      console.error(`推送主账号 ${mainAccountId} 新平台账号数据给用户失败:`, await userResponse.text());
    }

    console.log(`新平台账号 ${phone} 数据推送完成`);

  } catch (error) {
    console.error(`推送新平台账号 ${phone} 数据失败:`, error);
  }
}

/**
 * 批量获取新添加账号的数据并推送
 */
async function batchFetchAndPushNewAccounts(
  db: D1Database,
  mainAccountId: number,
  accountsData: { [phone: string]: PlatformAccountData },
  userAuthDO?: any
): Promise<void> {
  try {
    console.log(`开始批量获取 ${Object.keys(accountsData).length} 个新平台账号的数据...`);

    if (!userAuthDO) {
      console.log('UserAuthDO未提供，跳过推送');
      return;
    }

    const updatedAccounts: string[] = [];

    // 逐个获取账号数据
    for (const [phone, accountData] of Object.entries(accountsData)) {
      if (accountData.sessionid) {
        try {
          console.log(`获取新平台账号 ${phone} 的完整数据...`);
          const fetchResult = await fetchDataForNewAccount(
            phone,
            accountData.sessionid,
            accountData.username,
            accountData.homepage_url,
            accountData.is_verified
          );

          if (fetchResult.success && fetchResult.data) {
            // 更新账号数据
            const updateData: Partial<PlatformAccountData> = {
              data_update_time: new Date().toISOString()
            };

            // 更新收益和统计数据
            if (fetchResult.data.stats) {
              updateData.stats = {
                ...accountData.stats,
                ...fetchResult.data.stats
              };
            }

            // 更新用户名（如果获取到了新的）
            if (fetchResult.data.username) {
              updateData.username = fetchResult.data.username;
              console.log(`批量更新用户名: ${accountData.username} -> ${fetchResult.data.username}`);
            }

            // 更新主页URL（如果获取到了新的）
            if (fetchResult.data.homepage_url) {
              updateData.homepage_url = fetchResult.data.homepage_url;
              console.log(`批量更新主页URL: ${accountData.homepage_url} -> ${fetchResult.data.homepage_url}`);
            }

            // 更新账号状态
            if (fetchResult.accountOffline) {
              updateData.account_status = '掉线';
            } else if (fetchResult.data.account_status) {
              // 如果获取到了新的账号状态（正常/禁言），使用新状态
              updateData.account_status = fetchResult.data.account_status;
              console.log(`批量更新账号状态: ${accountData.account_status} -> ${fetchResult.data.account_status}`);
            } else if (accountData.account_status === '掉线') {
              updateData.account_status = '正常';
            }

            // 更新实名认证状态（如果获取到了新的）
            if (fetchResult.data.is_verified) {
              updateData.is_verified = fetchResult.data.is_verified;
              console.log(`批量更新实名认证状态: ${accountData.is_verified || '未设置'} -> ${fetchResult.data.is_verified}`);
            }

            // 添加昨日收益准备状态
            if (fetchResult.isYesterdayIncomeReady !== undefined) {
              (updateData as any).is_yesterday_income_ready = fetchResult.isYesterdayIncomeReady;
            }

            // 更新数据库中的账号信息
            await updateSinglePlatformAccount(db, mainAccountId, phone, updateData);
            updatedAccounts.push(phone);

            console.log(`新平台账号 ${phone} 完整数据获取成功`);
          } else {
            console.log(`新平台账号 ${phone} 数据获取失败: ${fetchResult.message}`);
          }
        } catch (fetchError) {
          console.error(`新平台账号 ${phone} 数据获取异常:`, fetchError);
        }
      }
    }

    // 如果有账号数据更新成功，推送给前端
    if (updatedAccounts.length > 0) {
      console.log(`${updatedAccounts.length} 个新平台账号数据获取成功，开始推送...`);
      await pushBatchNewPlatformAccountData(db, mainAccountId, updatedAccounts, userAuthDO);
    }

  } catch (error) {
    console.error('批量获取新平台账号数据失败:', error);
  }
}

/**
 * 推送批量新添加的平台账号数据给前端
 */
async function pushBatchNewPlatformAccountData(
  db: D1Database,
  mainAccountId: number,
  newAccountPhones: string[],
  userAuthDO: any
): Promise<void> {
  try {
    console.log(`开始推送批量新平台账号数据给前端，主账号: ${mainAccountId}, 新账号数: ${newAccountPhones.length}`);

    // 获取完整的主账号平台数据
    const platformData = await getPlatformAccountsData(db, mainAccountId);
    if (!platformData.success || !platformData.accounts) {
      console.error('获取主账号平台数据失败，无法推送');
      return;
    }

    const mainAccountData = platformData.accounts;
    const platformAccounts: any[] = [];

    // 收集该主账号下的所有平台账号数据
    for (const [accountPhone, accountData] of Object.entries(mainAccountData)) {
      const typedAccountData = accountData as PlatformAccountData;
      platformAccounts.push({
        phone: accountPhone,
        platform: typedAccountData.platform || '头条号',
        username: typedAccountData.username || '',
        login_type: typedAccountData.login_type || '',
        team_tag: typedAccountData.team_tag || '',
        account_status: typedAccountData.account_status || '',
        is_verified: typedAccountData.is_verified || '否', // 实名认证状态
        stats: {
          total_income: typedAccountData.stats?.total_income || '0',
          yesterday_income: typedAccountData.stats?.yesterday_income || '0',
          can_withdraw_amount: typedAccountData.stats?.can_withdraw_amount || '0',
          yesterday_reads: typedAccountData.stats?.yesterday_reads || '0',
          total_reads: typedAccountData.stats?.total_reads || '0',
          credit_score: typedAccountData.stats?.credit_score || '0',
          followers: typedAccountData.stats?.followers || '0'
        },
        data_update_time: typedAccountData.data_update_time || '',
        // 标记新添加的账号
        isNewlyAdded: newAccountPhones.includes(accountPhone)
      });
    }

    const groupData = {
      mainAccountId,
      platformAccounts,
      metadata: {
        last_batch_update: new Date().toISOString(),
        total_accounts: platformAccounts.length,
        updated_accounts: platformAccounts.filter(acc => acc.data_update_time).length
      }
    };

    // 1. 推送给管理员
    console.log('推送批量新平台账号数据给管理员...');
    const adminNotifyRequest = new Request('https://dummy.com/push-platform-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: 'batch_platform_accounts_added',
        target: 'admin',
        data: {
          mainAccountGroups: {
            [mainAccountId]: groupData
          },
          newAccountPhones: newAccountPhones,
          timestamp: new Date().toISOString()
        }
      })
    });

    const adminResponse = await userAuthDO.fetch(adminNotifyRequest as any);
    if (adminResponse.ok) {
      console.log('成功推送批量新平台账号数据给管理员');
    } else {
      console.error('推送管理员批量新平台账号数据失败:', await adminResponse.text());
    }

    // 2. 推送给该主账号的用户
    console.log(`推送批量新平台账号数据给主账号 ${mainAccountId} 的用户...`);
    const userNotifyRequest = new Request('https://dummy.com/push-platform-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: 'batch_platform_accounts_added',
        target: 'user',
        mainAccountId: mainAccountId,
        data: {
          mainAccountData: groupData,
          newAccountPhones: newAccountPhones,
          timestamp: new Date().toISOString()
        }
      })
    });

    const userResponse = await userAuthDO.fetch(userNotifyRequest as any);
    if (userResponse.ok) {
      console.log(`成功推送批量新平台账号数据给主账号 ${mainAccountId} 的用户`);
    } else {
      console.error(`推送主账号 ${mainAccountId} 批量新平台账号数据给用户失败:`, await userResponse.text());
    }

    console.log(`批量新平台账号数据推送完成，新账号: ${newAccountPhones.join(', ')}`);

  } catch (error) {
    console.error(`推送批量新平台账号数据失败:`, error);
  }
}

export async function deleteAccountInfoAPI(db: D1Database, id: number): Promise<Response> {
  return errorResponse('请使用新的deletePlatformAccountAPI', 400);
}

export async function getAccountInfoByPhoneAPI(db: D1Database, phone: string): Promise<Response> {
  return jsonResponse({
    success: true,
    accountInfo: undefined
  });
}

// 手动触发收益数据获取（管理员API）
export async function manualFetchIncomeDataAPI(db: D1Database): Promise<Response> {
  try {
    console.log('=== 管理员手动触发收益数据获取 ===');

    const result = await batchFetchAndUpdateData(db);

    console.log('批量获取结果:', {
      success: result.success,
      message: result.message,
      totalAccounts: result.totalAccounts,
      successCount: result.successCount,
      failureCount: result.failureCount,
      skippedCount: result.skippedCount
    });

    // 记录详细的结果信息
    if (result.results && result.results.length > 0) {
      console.log('详细结果:');
      result.results.forEach((item, index) => {
        console.log(`${index + 1}. ${item.phone}:`, {
          success: item.success,
          message: item.message,
          isYesterdayIncomeReady: item.isYesterdayIncomeReady,
          accountOffline: item.accountOffline,
          data: item.data
        });
      });
    }

    return jsonResponse({
      success: result.success,
      message: result.message,
      totalAccounts: result.totalAccounts,
      successCount: result.successCount,
      failureCount: result.failureCount,
      skippedCount: result.skippedCount,
      results: result.results
    });
  } catch (error) {
    console.error('手动触发收益数据获取失败:', error);
    return errorResponse(`手动触发收益数据获取失败: ${error}`, 500);
  }
}

// 手动更新全部信息API（收益、信用分、粉丝数、禁言状态、实名状态）
export async function manualUpdateAllDataAPI(db: D1Database): Promise<Response> {
  try {
    console.log('=== 管理员手动更新全部信息 ===');

    // 调用强制更新全部信息的函数
    const result = await batchFetchAndUpdateAllData(db);

    console.log('全部信息更新结果:', {
      success: result.success,
      message: result.message,
      totalAccounts: result.totalAccounts,
      successCount: result.successCount,
      failureCount: result.failureCount,
      skippedCount: result.skippedCount
    });

    // 记录详细的结果信息
    if (result.results && result.results.length > 0) {
      console.log('详细结果:');
      result.results.forEach((item, index) => {
        console.log(`${index + 1}. ${item.phone}:`, {
          success: item.success,
          message: item.message,
          data: item.data
        });
      });
    }

    return jsonResponse({
      success: result.success,
      message: result.message,
      totalAccounts: result.totalAccounts,
      successCount: result.successCount,
      failureCount: result.failureCount,
      skippedCount: result.skippedCount,
      results: result.results
    });
  } catch (error) {
    console.error('手动更新全部信息失败:', error);
    return errorResponse(`手动更新全部信息失败: ${error}`, 500);
  }
}



// API压力测试 - 单次收益数据获取（管理员API）
// 手动测试重置功能API
export async function testResetYesterdayDataAPI(db: D1Database): Promise<Response> {
  try {
    console.log('=== 手动执行昨日数据重置任务 ===');
    const resetResult = await resetYesterdayData(db);

    return jsonResponse({
      success: resetResult.success,
      message: resetResult.message,
      affectedAccounts: resetResult.affectedAccounts,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('手动重置任务执行失败:', error);
    return errorResponse(`手动重置任务执行失败: ${error}`, 500);
  }
}

export async function testIncomeDataAPI(request: Request): Promise<Response> {
  try {
    const requestBody = await request.json() as { sessionid?: string };
    const { sessionid } = requestBody;

    if (!sessionid) {
      return errorResponse('缺少sessionid参数', 400);
    }

    console.log(`API压力测试 - 获取收益数据，sessionid前8位: ${sessionid.substring(0, 8)}...`);

    const startTime = Date.now();

    // 调用收益数据API（使用正确的端点和方法）
    const response = await fetch('https://mp.toutiao.com/pgc/mp/income/income_statement_abstract?only_mid_income=false&days=30&app_id=1231', {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://mp.toutiao.com/profile_v4/analysis/income-overview',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Cookie': `sessionid=${sessionid}`
      }
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    if (!response.ok) {
      console.log(`API调用失败: HTTP ${response.status} ${response.statusText}`);
      return jsonResponse({
        success: false,
        code: response.status,
        message: `HTTP ${response.status}: ${response.statusText}`,
        responseTime
      });
    }

    const data = await response.json() as { code?: number; message?: string; data?: any };
    console.log(`API调用成功，响应时间: ${responseTime}ms，响应码: ${data.code}`);

    return jsonResponse({
      success: true,
      code: data.code,
      message: data.message || 'success',
      data: data.data,
      responseTime
    });

  } catch (error) {
    console.error('API压力测试失败:', error);
    return errorResponse(`API压力测试失败: ${error}`, 500);
  }
}

// 手动更新收益数据API（仅收益）
export async function manualUpdateIncomeAPI(db: D1Database): Promise<Response> {
  try {
    console.log('=== 管理员手动更新收益数据 ===');

    // 调用收益数据更新函数
    const result = await batchFetchAndUpdateIncomeOnly(db);

    console.log('收益数据更新结果:', {
      success: result.success,
      message: result.message,
      totalAccounts: result.totalAccounts,
      successCount: result.successCount,
      failureCount: result.failureCount,
      skippedCount: result.skippedCount
    });

    // 记录详细的结果信息
    if (result.results && result.results.length > 0) {
      console.log('详细结果:');
      result.results.forEach((item, index) => {
        console.log(`${index + 1}. ${item.phone}:`, {
          success: item.success,
          message: item.message,
          data: item.data
        });
      });
    }

    return jsonResponse({
      success: result.success,
      message: result.message,
      totalAccounts: result.totalAccounts,
      successCount: result.successCount,
      failureCount: result.failureCount,
      skippedCount: result.skippedCount,
      results: result.results
    });
  } catch (error) {
    console.error('手动更新收益数据失败:', error);
    return errorResponse(`手动更新收益数据失败: ${error}`, 500);
  }
}

// 手动更新信用分和粉丝数API
export async function manualUpdateCreditAndFansAPI(db: D1Database): Promise<Response> {
  try {
    console.log('=== 管理员手动更新信用分和粉丝数 ===');

    // 调用信用分和粉丝数更新函数
    const result = await batchFetchAndUpdateCreditAndFans(db);

    console.log('信用分和粉丝数更新结果:', {
      success: result.success,
      message: result.message,
      totalAccounts: result.totalAccounts,
      successCount: result.successCount,
      failureCount: result.failureCount,
      skippedCount: result.skippedCount
    });

    // 记录详细的结果信息
    if (result.results && result.results.length > 0) {
      console.log('详细结果:');
      result.results.forEach((item, index) => {
        console.log(`${index + 1}. ${item.phone}:`, {
          success: item.success,
          message: item.message,
          data: item.data
        });
      });
    }

    return jsonResponse({
      success: result.success,
      message: result.message,
      totalAccounts: result.totalAccounts,
      successCount: result.successCount,
      failureCount: result.failureCount,
      skippedCount: result.skippedCount,
      results: result.results
    });
  } catch (error) {
    console.error('手动更新信用分和粉丝数失败:', error);
    return errorResponse(`手动更新信用分和粉丝数失败: ${error}`, 500);
  }
}

// 手动更新账号详细信息API
export async function manualUpdateAccountInfoAPI(db: D1Database): Promise<Response> {
  try {
    console.log('=== 管理员手动更新账号详细信息 ===');

    // 调用账号详细信息更新函数
    const result = await batchFetchAndUpdateAccountInfo(db);

    console.log('账号详细信息更新结果:', {
      success: result.success,
      message: result.message,
      totalAccounts: result.totalAccounts,
      successCount: result.successCount,
      failureCount: result.failureCount,
      skippedCount: result.skippedCount
    });

    // 记录详细的结果信息
    if (result.results && result.results.length > 0) {
      console.log('详细结果:');
      result.results.forEach((item, index) => {
        console.log(`${index + 1}. ${item.phone}:`, {
          success: item.success,
          message: item.message,
          data: item.data
        });
      });
    }

    return jsonResponse({
      success: result.success,
      message: result.message,
      totalAccounts: result.totalAccounts,
      successCount: result.successCount,
      failureCount: result.failureCount,
      skippedCount: result.skippedCount,
      results: result.results
    });
  } catch (error) {
    console.error('手动更新账号详细信息失败:', error);
    return errorResponse(`手动更新账号详细信息失败: ${error}`, 500);
  }
}

// 手动更新账号状态API
export async function manualUpdateAccountStatusAPI(db: D1Database): Promise<Response> {
  try {
    console.log('=== 管理员手动更新账号状态 ===');

    // 调用账号状态更新函数
    const result = await batchFetchAndUpdateAccountStatus(db);

    console.log('账号状态更新结果:', {
      success: result.success,
      message: result.message,
      totalAccounts: result.totalAccounts,
      successCount: result.successCount,
      failureCount: result.failureCount,
      skippedCount: result.skippedCount
    });

    // 记录详细的结果信息
    if (result.results && result.results.length > 0) {
      console.log('详细结果:');
      result.results.forEach((item, index) => {
        console.log(`${index + 1}. ${item.phone}:`, {
          success: item.success,
          message: item.message,
          data: item.data
        });
      });
    }

    return jsonResponse({
      success: result.success,
      message: result.message,
      totalAccounts: result.totalAccounts,
      successCount: result.successCount,
      failureCount: result.failureCount,
      skippedCount: result.skippedCount,
      results: result.results
    });
  } catch (error) {
    console.error('手动更新账号状态失败:', error);
    return errorResponse(`手动更新账号状态失败: ${error}`, 500);
  }
}



